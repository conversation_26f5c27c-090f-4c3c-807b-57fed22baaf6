<?php
/**
 * VedMG ClassRoom Course Management Page
 * 
 * This page handles course management functionality.
 * Allows viewing courses and course actions.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed courses management page');

// Handle pagination and filtering parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, intval($_GET['per_page']))) : 10;
$search_filter = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get paginated data from database
$course_data = VedMG_ClassRoom_Database_Helper::get_courses($current_page, $per_page, $search_filter);
$courses = $course_data['courses'];
$total_count = $course_data['total_count'];
$total_pages = $course_data['total_pages'];

// Get classroom options for dropdowns
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_count);

// Log filtering if active
if ($search_filter) {
    vedmg_log_info('ADMIN', 'Filtering courses - Search: ' . $search_filter);
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Course Management</h1>
        <p>Manage your courses and view course information</p>
    </div>
    
    <!-- Course Management Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>All Courses</h2>
            

            
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn" id="refresh-courses">
                    Refresh Courses
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="sync-masterstudy">
                    Sync with MasterStudy LMS
                </button>
            </div>
        </div>
        
        <!-- Course Filter Section -->
        <form method="GET" class="vedmg-filter-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-filter-group">
                <label for="search">Search Courses:</label>
                <input type="text" name="search" id="search" class="vedmg-filter-input" 
                       value="<?php echo esc_attr($search_filter); ?>" 
                       placeholder="Search by course name, description, or instructor...">
            </div>
            
            <div class="vedmg-filter-group">
                <label for="per_page">Items per page:</label>
                <select name="per_page" id="per_page" class="vedmg-filter-select">
                    <option value="10" <?php selected($per_page, 10); ?>>10</option>
                    <option value="25" <?php selected($per_page, 25); ?>>25</option>
                    <option value="50" <?php selected($per_page, 50); ?>>50</option>
                    <option value="100" <?php selected($per_page, 100); ?>>100</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <button type="submit" class="vedmg-classroom-btn">Apply Filters</button>
                <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>" 
                   class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear Filters</a>
            </div>
        </form>
        
        <!-- Course Summary -->
        <div class="vedmg-enrollment-summary">
            <span>Total: <strong><?php echo $total_count; ?></strong></span>
            <span>Showing: <strong><?php echo $start_item; ?>-<?php echo $end_item; ?></strong></span>
            <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
        </div>

        <p>Here you can view all courses created by instructors.</p>
        
        <!-- Course Management Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th>Course Name</th>
                    <th>Instructor</th>
                    <th>Created Date</th>
                    <th>Students Enrolled</th>
                    <th>Classroom Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($courses)): ?>
                    <?php foreach ($courses as $course): ?>
                        <tr class="real-data-row" data-course-id="<?php echo $course->course_id; ?>">
                            <td>
                                <strong><?php echo esc_html($course->course_name); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($course->instructor_name ?: 'Unknown'); ?></td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_date($course->created_date); ?></td>
                            <td>
                                <span class="vedmg-student-count"><?php echo intval($course->student_count); ?></span>
                                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-enrollments&course_id=' . $course->course_id); ?>" class="vedmg-view-students">
                                    View Students
                                </a>
                            </td>
                            <td>
                                <span class="vedmg-classroom-status" data-status="<?php echo $course->classroom_status; ?>">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_classroom_status($course->classroom_status); ?>
                                </span>
                            </td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <?php if ($course->classroom_status === 'pending'): ?>
                                        <button class="vedmg-classroom-btn vedmg-create-classroom-btn"
                                                data-course-id="<?php echo $course->course_id; ?>"
                                                data-course-name="<?php echo esc_attr($course->course_name); ?>"
                                                data-instructor-email="<?php echo esc_attr($course->instructor_email); ?>"
                                                data-instructor-name="<?php echo esc_attr($course->instructor_name); ?>">
                                            <span class="vedmg-classroom-spinner"></span>
                                            Create Classroom
                                        </button>
                                    <?php elseif ($course->classroom_status === 'active'): ?>
                                        <button class="vedmg-classroom-btn vedmg-classroom-btn-warning vedmg-archive-classroom-btn"
                                                data-course-id="<?php echo $course->course_id; ?>"
                                                data-classroom-id="<?php echo esc_attr($course->google_classroom_id); ?>">
                                            <span class="vedmg-classroom-spinner"></span>
                                            Archive Classroom
                                        </button>

                                        <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-delete-classroom-btn"
                                                data-course-id="<?php echo $course->course_id; ?>"
                                                data-classroom-id="<?php echo esc_attr($course->google_classroom_id); ?>">
                                            <span class="vedmg-classroom-spinner"></span>
                                            Delete Classroom
                                        </button>
                                    <?php elseif ($course->classroom_status === 'archived'): ?>
                                        <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-delete-classroom-btn"
                                                data-course-id="<?php echo $course->course_id; ?>"
                                                data-classroom-id="<?php echo esc_attr($course->google_classroom_id); ?>">
                                            <span class="vedmg-classroom-spinner"></span>
                                            Delete Classroom
                                        </button>

                                        <span class="vedmg-status-badge vedmg-status-archived">Archived</span>
                                    <?php endif; ?>

                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-info vedmg-view-course-btn" data-course-id="<?php echo $course->course_id; ?>">
                                        View Details
                                    </button>

                                    <?php if ($course->classroom_status === 'active'): ?>
                                        <?php if (!empty($course->meeting_link)): ?>
                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-info vedmg-meeting-link-btn" data-course-id="<?php echo $course->course_id; ?>" data-has-link="true">
                                                <span class="vedmg-classroom-spinner"></span>
                                                View Meeting Link
                                            </button>
                                        <?php else: ?>
                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-success vedmg-meeting-link-btn" data-course-id="<?php echo $course->course_id; ?>" data-has-link="false">
                                                <span class="vedmg-classroom-spinner"></span>
                                                Generate Meeting Link
                                            </button>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (empty($courses)): ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                            <em>No courses found in database. Activate the plugin to create database tables, then add some courses.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Server-Side Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination">
        <div class="vedmg-pagination-info">
            Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_count; ?> courses
        </div>
        <div class="vedmg-pagination-controls">
            <?php
            $base_url = admin_url('admin.php');
            $query_args = array_merge($_GET, array('page' => $_GET['page']));
            
            // First page
            if ($current_page > 1):
                $first_url = add_query_arg(array_merge($query_args, array('paged' => 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($first_url); ?>" class="vedmg-pagination-btn">‹‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹‹</span>
            <?php endif; ?>
            
            <?php
            // Previous page
            if ($current_page > 1):
                $prev_url = add_query_arg(array_merge($query_args, array('paged' => $current_page - 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($prev_url); ?>" class="vedmg-pagination-btn">‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹</span>
            <?php endif; ?>
            
            <div class="vedmg-pagination-numbers">
                <?php
                // Calculate page range
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++):
                    if ($i === $current_page):
                ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else:
                    $page_url = add_query_arg(array_merge($query_args, array('paged' => $i)), $base_url);
                ?>
                    <a href="<?php echo esc_url($page_url); ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif; endfor; ?>
            </div>
            
            <?php
            // Next page
            if ($current_page < $total_pages):
                $next_url = add_query_arg(array_merge($query_args, array('paged' => $current_page + 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($next_url); ?>" class="vedmg-pagination-btn">›</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">›</span>
            <?php endif; ?>
            
            <?php
            // Last page
            if ($current_page < $total_pages):
                $last_url = add_query_arg(array_merge($query_args, array('paged' => $total_pages)), $base_url);
            ?>
                <a href="<?php echo esc_url($last_url); ?>" class="vedmg-pagination-btn">››</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">››</span>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Meeting Link Modal -->
<div id="vedmg-meeting-link-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3 id="meeting-modal-title">Generate/Update Meeting Link</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-meeting-link-form">
                <input type="hidden" id="meeting-course-id" name="course_id" value="">
                
                <div class="vedmg-form-group">
                    <label for="meeting-title">Meeting Title:</label>
                    <input type="text" id="meeting-title" name="meeting_title" class="vedmg-form-control" readonly>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="meeting-description">Meeting Description:</label>
                    <textarea id="meeting-description" name="meeting_description" class="vedmg-form-control" rows="4" readonly></textarea>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="meeting-link">Meeting Link:</label>
                    <input type="url" id="meeting-link" name="meeting_link" class="vedmg-form-control" placeholder="Enter Google Meet or Zoom link manually" required>
                    <p class="description">Enter the Google Meet, Zoom, or other meeting platform link for this session.</p>

                    <!-- Edit checkbox - only shown when link exists -->
                    <div id="meeting-link-edit-section" style="display: none; margin-top: 10px;">
                        <label>
                            <input type="checkbox" id="enable-meeting-edit" name="enable_meeting_edit" value="1">
                            Enable editing of meeting link
                        </label>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="assigned-teacher">Assign to Teacher:</label>
                    <select id="assigned-teacher" name="assigned_teacher" class="vedmg-form-control" disabled>
                        <option value="">Select a teacher...</option>
                        <!-- Teachers will be loaded dynamically -->
                    </select>
                    <p class="description">Select the teacher who will conduct this meeting session.</p>
                </div>
                

                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-meeting-link">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-meeting-link">
                        <span class="vedmg-classroom-spinner"></span>
                        <span id="save-meeting-link-text">Save Meeting Link</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.vedmg-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

/* Management Modal Specific Styles */
.vedmg-management-modal {
    padding: 20px 0;
}

.vedmg-management-modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    margin: 2% auto;
}

.vedmg-management-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 25px;
}

/* Management Grid */
.vedmg-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-management-card {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.vedmg-management-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #0073aa;
}

.vedmg-management-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.vedmg-management-card p {
    color: #666;
    font-size: 14px;
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.vedmg-management-card .vedmg-classroom-btn {
    width: 100%;
    justify-content: center;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
}

.vedmg-modal-header h3 {
    margin: 0;
    color: #333;
}

.vedmg-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    padding: 5px;
    line-height: 1;
    border: none;
    background: none;
    outline: none;
}

.vedmg-modal-close:hover {
    color: #000;
}

.vedmg-modal-body {
    padding: 25px;
}

.vedmg-form-group {
    margin-bottom: 20px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.vedmg-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.vedmg-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-form-row {
    display: flex;
    gap: 15px;
}

.vedmg-form-half {
    flex: 1;
}

.vedmg-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .vedmg-management-modal-content {
        margin: 1% auto;
        max-height: 95vh;
    }
    
    .vedmg-management-modal-body {
        max-height: 80vh;
        padding: 15px;
    }
    
    .vedmg-management-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .vedmg-management-card {
        padding: 15px;
    }
    
    .vedmg-modal-header {
        padding: 15px 20px;
    }
    
    .vedmg-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .vedmg-form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .vedmg-modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .vedmg-management-modal-content {
        max-height: 98vh;
    }
    
    .vedmg-modal-header h3 {
        font-size: 16px;
    }
    
    .vedmg-management-card h4 {
        font-size: 14px;
    }
    
    .vedmg-management-card p {
        font-size: 13px;
    }
}



.vedmg-section-header {
    display: flex;
    flex-direction: column;
}

.vedmg-section-header h2 {
    margin-bottom: 10px;
}

/* Loading states */
.vedmg-loading {
    opacity: 0.6;
    pointer-events: none;
}

.vedmg-loading::after {
    content: " Loading...";
    font-style: italic;
}

/* New Classroom Management Styles */
.vedmg-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 5px;
}

.vedmg-status-archived {
    background-color: #f39c12;
    color: white;
}

.vedmg-classroom-btn-success {
    background-color: #27ae60;
    color: white;
    border: 1px solid #229954;
}

.vedmg-classroom-btn-success:hover {
    background-color: #229954;
}

.vedmg-classroom-btn-warning {
    background-color: #f39c12;
    color: white;
    border: 1px solid #e67e22;
}

.vedmg-classroom-btn-warning:hover {
    background-color: #e67e22;
}

.vedmg-classroom-btn-danger {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

.vedmg-classroom-btn-danger:hover {
    background-color: #c0392b;
}

.vedmg-classroom-spinner {
    display: none;
    width: 12px;
    height: 12px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

.vedmg-classroom-spinner.spinning {
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.vedmg-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
}

.vedmg-action-buttons .vedmg-classroom-btn {
    margin: 2px;
    padding: 6px 12px;
    font-size: 12px;
    white-space: nowrap;
}

/* Loading Overlay Styles */
.vedmg-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: none;
}

.vedmg-loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 300px;
}

.vedmg-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: vedmg-spin 1s linear infinite;
    margin: 0 auto 20px;
}

.vedmg-loading-message {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-top: 10px;
}

@keyframes vedmg-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Popup Styles */
.vedmg-custom-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.vedmg-custom-popup {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    margin: auto;
    position: relative;
    animation: vedmg-popup-appear 0.3s ease-out;
}

@keyframes vedmg-popup-appear {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.vedmg-popup-header {
    background: #f8f9fa;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
}

.vedmg-popup-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.vedmg-popup-body {
    padding: 24px;
}

.vedmg-popup-body p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    color: #555;
}

.vedmg-popup-footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.vedmg-popup-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.vedmg-popup-btn-primary {
    background: #007cba;
    color: white;
}

.vedmg-popup-btn-primary:hover {
    background: #005a87;
}

.vedmg-popup-btn-confirm {
    background: #dc3545;
    color: white;
}

.vedmg-popup-btn-confirm:hover {
    background: #c82333;
}

.vedmg-popup-btn-cancel {
    background: #6c757d;
    color: white;
}

.vedmg-popup-btn-cancel:hover {
    background: #5a6268;
}

.vedmg-popup-btn:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .vedmg-custom-popup {
        max-width: 95%;
        margin: 10px;
    }

    .vedmg-popup-header,
    .vedmg-popup-body,
    .vedmg-popup-footer {
        padding: 16px;
    }

    .vedmg-popup-footer {
        flex-direction: column;
        gap: 8px;
    }

    .vedmg-popup-btn {
        width: 100%;
    }
}

/* Ensure popup is always visible */
.vedmg-custom-popup-overlay.show {
    display: flex !important;
}


</style>

<script>
// Course management specific JavaScript
jQuery(document).ready(function($) {
    console.log('Course management page loaded');

    // Handle Create Classroom button
    $('.vedmg-create-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const courseName = button.data('course-name');
        const instructorEmail = button.data('instructor-email');
        const instructorName = button.data('instructor-name');

        if (!confirm(`Are you sure you want to create a Google Classroom for "${courseName}"?`)) {
            return;
        }

        // Show loading state
        button.prop('disabled', true);
        button.find('.vedmg-classroom-spinner').addClass('spinning');
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Creating Classroom...');

        // Show overlay loader
        showLoadingOverlay('Creating Google Classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_create_classroom',
                course_id: courseId,
                course_name: courseName,
                instructor_email: instructorEmail,
                instructor_name: instructorName,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    let message = 'Classroom created successfully!\n\n' +
                                  'Classroom ID: ' + response.data.classroom_id + '\n' +
                                  'Enrollment Code: ' + (response.data.invitation_code || 'N/A') + '\n' +
                                  'Course State: ' + (response.data.course_state || 'ACTIVE') + '\n' +
                                  'Classroom Link: ' + (response.data.classroom_link || 'N/A');

                    alert(message);
                    location.reload(); // Refresh page to show updated status
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred while creating the classroom.');
            },
            complete: function() {
                button.prop('disabled', false);
                button.find('.vedmg-classroom-spinner').removeClass('spinning');
                button.html(originalText);
                hideLoadingOverlay();
            }
        });
    });

    // Handle Archive Classroom button
    $('.vedmg-archive-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const classroomId = button.data('classroom-id');

        if (!confirm('Are you sure you want to archive this classroom? Students will no longer be able to access it.')) {
            return;
        }

        // Show loading state
        button.prop('disabled', true);
        button.find('.vedmg-classroom-spinner').addClass('spinning');
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Archiving...');

        // Show overlay loader
        showLoadingOverlay('Archiving Google Classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_archive_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    let message = 'Classroom archived successfully!';

                    // Add development mode notice if using mock response
                    if (response.data.mock_response) {
                        message += '\n\n⚠️ DEVELOPMENT MODE: This is a mock response for testing. Please archive the classroom manually in Google Classroom.';
                    }

                    alert(message);
                    location.reload(); // Refresh page to show updated status
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred while archiving the classroom.');
            },
            complete: function() {
                button.prop('disabled', false);
                button.find('.vedmg-classroom-spinner').removeClass('spinning');
                button.html(originalText);
                hideLoadingOverlay();
            }
        });
    });

    // Handle Delete Classroom button with two-step process
    $('.vedmg-delete-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const classroomId = button.data('classroom-id');

        // Step 1: Show initial confirmation popup
        showCustomConfirmation(
            'Delete Classroom',
            'Are you sure you want to delete this classroom? This will first archive the classroom and then permanently delete it.',
            'Delete Classroom',
            'Cancel',
            function() {
                // User confirmed, start the two-step deletion process
                startTwoStepDeletion(button, courseId, classroomId);
            }
        );
    });

    // Two-step deletion process
    function startTwoStepDeletion(button, courseId, classroomId) {
        // Step 1: Archive the classroom first
        button.prop('disabled', true);
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Archiving...');

        showLoadingOverlay('Archiving classroom before deletion...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_archive_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                hideLoadingOverlay();

                if (response.success) {
                    // Step 2: Show archive success and ask for final deletion confirmation
                    showCustomConfirmation(
                        'Classroom Archived',
                        'The classroom has been successfully archived. Do you want to permanently delete it now? This action cannot be undone.',
                        'Delete Permanently',
                        'Keep Archived',
                        function() {
                            // User confirmed final deletion
                            performFinalDeletion(button, courseId, classroomId, originalText);
                        },
                        function() {
                            // User chose to keep archived
                            button.prop('disabled', false);
                            button.html(originalText);
                            location.reload(); // Refresh to show archived status
                        }
                    );
                } else {
                    button.prop('disabled', false);
                    button.html(originalText);
                    showCustomAlert('Archive Failed', 'Failed to archive classroom: ' + response.data);
                }
            },
            error: function() {
                hideLoadingOverlay();
                button.prop('disabled', false);
                button.html(originalText);
                showCustomAlert('Error', 'An error occurred while archiving the classroom.');
            }
        });
    }

    // Perform final deletion after archiving
    function performFinalDeletion(button, courseId, classroomId, originalText) {
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Deleting...');
        showLoadingOverlay('Permanently deleting classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_delete_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                hideLoadingOverlay();

                if (response.success) {
                    showCustomAlert('Success', 'Classroom deleted successfully!', function() {
                        location.reload();
                    });
                } else {
                    button.prop('disabled', false);
                    button.html(originalText);
                    showCustomAlert('Delete Failed', 'Failed to delete classroom: ' + response.data);
                }
            },
            error: function() {
                hideLoadingOverlay();
                button.prop('disabled', false);
                button.html(originalText);
                showCustomAlert('Error', 'An error occurred while deleting the classroom.');
            }
        });
    }

    // Custom confirmation popup
    function showCustomConfirmation(title, message, confirmText, cancelText, onConfirm, onCancel) {
        const popup = $(`
            <div class="vedmg-custom-popup-overlay">
                <div class="vedmg-custom-popup">
                    <div class="vedmg-popup-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="vedmg-popup-body">
                        <p>${message}</p>
                    </div>
                    <div class="vedmg-popup-footer">
                        <button class="vedmg-popup-btn vedmg-popup-btn-cancel">${cancelText}</button>
                        <button class="vedmg-popup-btn vedmg-popup-btn-confirm">${confirmText}</button>
                    </div>
                </div>
            </div>
        `);

        $('body').append(popup);
        popup.addClass('show').fadeIn(300);

        // Ensure popup is centered and visible
        setTimeout(function() {
            popup.css('display', 'flex');
        }, 10);

        popup.find('.vedmg-popup-btn-confirm').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onConfirm) onConfirm();
        });

        popup.find('.vedmg-popup-btn-cancel').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onCancel) onCancel();
        });

        // Close on overlay click
        popup.on('click', function(e) {
            if (e.target === popup[0]) {
                popup.fadeOut(300, function() { popup.remove(); });
                if (onCancel) onCancel();
            }
        });
    }

    // Custom alert popup
    function showCustomAlert(title, message, onClose) {
        const popup = $(`
            <div class="vedmg-custom-popup-overlay">
                <div class="vedmg-custom-popup">
                    <div class="vedmg-popup-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="vedmg-popup-body">
                        <p>${message}</p>
                    </div>
                    <div class="vedmg-popup-footer">
                        <button class="vedmg-popup-btn vedmg-popup-btn-primary">OK</button>
                    </div>
                </div>
            </div>
        `);

        $('body').append(popup);
        popup.addClass('show').fadeIn(300);

        // Ensure popup is centered and visible
        setTimeout(function() {
            popup.css('display', 'flex');
        }, 10);

        popup.find('.vedmg-popup-btn-primary').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onClose) onClose();
        });

        // Close on overlay click
        popup.on('click', function(e) {
            if (e.target === popup[0]) {
                popup.fadeOut(300, function() { popup.remove(); });
                if (onClose) onClose();
            }
        });
    }

    // Loading overlay functions
    function showLoadingOverlay(message) {
        const overlay = $('<div class="vedmg-loading-overlay">' +
            '<div class="vedmg-loading-content">' +
            '<div class="vedmg-loading-spinner"></div>' +
            '<div class="vedmg-loading-message">' + message + '</div>' +
            '</div>' +
            '</div>');
        $('body').append(overlay);
        overlay.fadeIn(300);
    }

    function hideLoadingOverlay() {
        $('.vedmg-loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    // Any course-specific JavaScript will be handled by courses.js
});
</script>
