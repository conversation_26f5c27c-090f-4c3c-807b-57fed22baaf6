<?php
/*
Plugin Name: VedMG ClassRoom
Description: A plugin to manage classroom activities, including student registration, course management, and attendance tracking.
Plugin URI: https://vedmg.com
Version: 1.0
Author: <PERSON><PERSON><PERSON> Mishra
Author URI: https://vedmg.com
*/

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * Define plugin constants
 * These constants will be used throughout the plugin for paths and URLs
 */
define('VEDMG_CLASSROOM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('VEDMG_CLASSROOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('VEDMG_CLASSROOM_VERSION', '1.0');

/**
 * Include required files
 * Load all the necessary files for the plugin to function
 */
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'Debug/debugLog.php';
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'class-core.php';
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/upgrade.php';
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'api/api.php';
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'api/schedule_lab_handler.php';

// Load integrations when plugins are loaded
add_action('plugins_loaded', function() {
    // Load WooCommerce integration if WooCommerce is active
    if (class_exists('WooCommerce')) {
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/woocommerce.php';
        VedMG_ClassRoom_WooCommerce_Integration::init();
        vedmg_log_info('INTEGRATION', 'WooCommerce integration loaded and initialized');
    }

    // Load MasterStudy integration if MasterStudy LMS is active
    if (class_exists('MasterStudy\Lms\Plugin') || function_exists('stm_lms_templates')) {
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'integrations/masterstudy.php';
        VedMG_ClassRoom_MasterStudy_Integration::init();
        vedmg_log_info('INTEGRATION', 'MasterStudy LMS integration loaded and initialized');
    }
});

/**
 * Initialize the plugin
 * This function runs when the plugin is loaded
 */
function vedmg_classroom_init() {
    // Log plugin initialization
    vedmg_log_info('CORE', 'VedMG ClassRoom plugin initialized');

    // CRITICAL FIX: One-time emergency database check (only if needed)
    if (is_admin()) {
        $last_emergency_check = get_option('vedmg_classroom_emergency_check', 0);
        $current_time = time();

        // Only run emergency check once per week to avoid overhead
        if (($current_time - $last_emergency_check) > WEEK_IN_SECONDS) {
            global $wpdb;
            $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");

            if ($sessions_exists) {
                // Quick check for one critical column
                $has_featured = $wpdb->get_var("SHOW COLUMNS FROM $sessions_table LIKE 'is_featured'");

                if (!$has_featured) {
                    vedmg_log_warning('DATABASE', 'Critical columns missing, running emergency repair...');
                    VedMG_ClassRoom_Database_Upgrade::emergency_repair();
                }
            }

            // Update last check timestamp
            update_option('vedmg_classroom_emergency_check', $current_time);
        }
    }

    // Check for database upgrades
    VedMG_ClassRoom_Database_Upgrade::check_and_upgrade();

    // Initialize the core class
    VedMG_ClassRoom_Core::init();
}

/**
 * Plugin activation hook
 * This function runs when the plugin is activated
 */
function vedmg_classroom_activate() {
    // Log plugin activation
    vedmg_log_info('CORE', 'VedMG ClassRoom plugin activated');
    
    // Run activation tasks (database creation, etc.)
    VedMG_ClassRoom_Core::activate();
}

/**
 * Plugin deactivation hook
 * This function runs when the plugin is deactivated
 */
function vedmg_classroom_deactivate() {
    // Log plugin deactivation
    vedmg_log_info('CORE', 'VedMG ClassRoom plugin deactivated');
    
    // Run deactivation tasks
    VedMG_ClassRoom_Core::deactivate();
}

// Hook into WordPress
add_action('plugins_loaded', 'vedmg_classroom_init');
register_activation_hook(__FILE__, 'vedmg_classroom_activate');
register_deactivation_hook(__FILE__, 'vedmg_classroom_deactivate');


// Protect user ID 24 from deletion
add_action('delete_user', function($user_id) {
    if ($user_id == 24) {
        wp_die('User ID 24 (Kushagra Mishra) cannot be deleted as it is required for course management.', 'User Deletion Prevented', array('back_link' => true));
    }
}, 1);

add_action('wp_delete_user', function($user_id) {
    if ($user_id == 24) {
        wp_die('User ID 24 (Kushagra Mishra) cannot be deleted as it is required for course management.', 'User Deletion Prevented', array('back_link' => true));
    }
}, 1);

?>