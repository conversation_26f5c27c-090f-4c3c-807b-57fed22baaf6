<?php
/**
 * Schedule Lab Handler
 * 
 * Handles the three types of lab scheduling:
 * 1. Individual Session - Single student
 * 2. Group Session - Multiple selected students  
 * 3. Class-wide Session - All students in course
 * 
 * @package VedMG_ClassRoom
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Handler file loaded

class VedMG_Schedule_Lab_Handler {

    private static $api_endpoint = 'https://gclassroom-839391304260.us-central1.run.app/share_invite'; // Real API
    private static $api_key = 'G$$gle@VedMG!@#';
    private static $test_mode = false; // Set to true for testing without hitting real API
    
    /**
     * Handle schedule lab request
     */
    public static function handle_schedule_request() {
        try {
            // Ensure clean JSON output
            ob_clean();
            header('Content-Type: application/json');

            // Suppress any PHP warnings/notices that could break JSON
            error_reporting(E_ERROR);
            ini_set('display_errors', 0);

            vedmg_log_info('SCHEDULE_LAB', 'Schedule lab request received', $_POST);

            // Check if required functions exist
            if (!function_exists('wp_verify_nonce')) {
                vedmg_log_error('SCHEDULE_LAB', 'wp_verify_nonce function not available');
                wp_send_json_error('WordPress functions not available');
                return;
            }

            // Verify nonce for security
            $nonce_action = 'vedmg_classroom_nonce';
            $provided_nonce = $_POST['nonce'] ?? '';

            vedmg_log_info('SCHEDULE_LAB', 'Nonce verification', [
                'provided_nonce' => $provided_nonce,
                'expected_action' => $nonce_action,
                'verification_result' => wp_verify_nonce($provided_nonce, $nonce_action)
            ]);

            // Check nonce verification
            $nonce_valid = wp_verify_nonce($provided_nonce, $nonce_action);
            if (!$nonce_valid) {
                vedmg_log_error('SCHEDULE_LAB', 'Nonce verification failed', [
                    'provided_nonce' => $provided_nonce,
                    'expected_action' => $nonce_action
                ]);
                wp_send_json_error('Security check failed');
                return;
            }

            vedmg_log_info('SCHEDULE_LAB', 'Nonce verification passed');

        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Error in initial setup', $e->getMessage());
            wp_send_json_error('Initial setup error: ' . $e->getMessage());
            return;
        }

        try {
            // Get session type
            $session_type = sanitize_text_field($_POST['session_type']);
            vedmg_log_info('SCHEDULE_LAB', "Session type: $session_type");

            $session_data = self::sanitize_session_data($_POST);
            vedmg_log_info('SCHEDULE_LAB', "Starting $session_type session scheduling", $session_data);

        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Error in session data processing', $e->getMessage());
            wp_send_json_error('Session data processing error: ' . $e->getMessage());
            return;
        }

        try {
            switch ($session_type) {
                case 'individual':
                    $result = self::handle_individual_session($session_data);
                    break;
                    
                case 'group':
                    $result = self::handle_group_session($session_data);
                    break;
                    
                case 'class':
                    $result = self::handle_class_wide_session($session_data);
                    break;
                    
                default:
                    throw new Exception('Invalid session type: ' . $session_type);
            }
            
            // Ensure clean JSON output
            ob_clean();
            wp_send_json_success($result);
            
        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Session scheduling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'post_data' => $_POST
            ]);
            // Ensure clean JSON output
            ob_clean();
            wp_send_json_error([
                'message' => $e->getMessage(),
                'type' => 'error',
                'debug_info' => [
                    'session_type' => $session_type ?? 'unknown',
                    'error_line' => $e->getLine(),
                    'error_file' => basename($e->getFile())
                ]
            ]);
        } catch (Error $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Fatal error in session scheduling', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'post_data' => $_POST
            ]);
            // Ensure clean JSON output
            ob_clean();
            wp_send_json_error([
                'message' => 'Fatal error: ' . $e->getMessage(),
                'type' => 'fatal_error'
            ]);
        }
    }
    
    /**
     * Handle individual session (single student)
     */
    private static function handle_individual_session($session_data) {
        vedmg_log_info('SCHEDULE_LAB', 'Processing individual session');

        try {
            // Get student data
            $student_id = intval($session_data['student_id']);
            vedmg_log_info('SCHEDULE_LAB', "Getting student data for ID: $student_id");

            $student_data = self::get_student_data($student_id);

            if (!$student_data) {
                throw new Exception('Student not found: ' . $student_id);
            }

            vedmg_log_info('SCHEDULE_LAB', 'Student data retrieved', $student_data);

            // Get course data
            vedmg_log_info('SCHEDULE_LAB', "Getting course data for ID: {$session_data['course_id']}");
            $course_data = self::get_course_data($session_data['course_id']);

            if (!$course_data) {
                throw new Exception('Course not found: ' . $session_data['course_id']);
            }

            vedmg_log_info('SCHEDULE_LAB', 'Course data retrieved successfully');

        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Error in individual session data retrieval', $e->getMessage());
            throw $e;
        }
        
        // Build API request
        $api_request = self::build_api_request($session_data, $student_data, $course_data);
        
        // Make API call
        $api_response = self::make_api_call($api_request);
        
        // Store session in database
        $session_id = self::store_session($session_data, $student_data, $api_response);

        // Update enrollment scheduling tracking (v2.0)
        $enrollment_id = self::get_enrollment_id($student_data['id'], $session_data['course_id']);
        if ($enrollment_id) {
            VedMG_ClassRoom_Database_Helper::update_enrollment_scheduling($enrollment_id, 'individual', $session_data);

            // Add detailed session tracking
            $google_event_id = isset($api_response['event_id']) ? $api_response['event_id'] : null;
            VedMG_ClassRoom_Database_Helper::add_session_tracking(
                $enrollment_id,
                $student_data['id'],
                $session_data['course_id'],
                $session_id,
                'individual',
                $session_data['session_date'] . ' ' . $session_data['start_time'],
                $session_data,
                $google_event_id
            );
        }

        vedmg_log_info('SCHEDULE_LAB', 'Individual session completed successfully', [
            'session_id' => $session_id,
            'enrollment_id' => $enrollment_id,
            'student_email' => $student_data['email']
        ]);

        return [
            'type' => 'individual',
            'sessions_created' => 1,
            'students_notified' => 1,
            'session_id' => $session_id,
            'student_email' => $student_data['email'],
            'affected_students' => [$student_data['id']], // Add this for frontend
            'api_response' => $api_response
        ];
    }
    
    /**
     * Handle group session (multiple selected students)
     */
    private static function handle_group_session($session_data) {
        vedmg_log_info('SCHEDULE_LAB', 'Processing group session');
        
        $student_ids = $session_data['student_ids']; // Array of student IDs
        $course_data = self::get_course_data($session_data['course_id']);
        
        if (!$course_data) {
            throw new Exception('Course not found: ' . $session_data['course_id']);
        }
        
        $results = [];
        $success_count = 0;
        $error_count = 0;
        
        foreach ($student_ids as $student_id) {
            try {
                $student_data = self::get_student_data($student_id);
                
                if (!$student_data) {
                    vedmg_log_error('SCHEDULE_LAB', 'Student not found in group', $student_id);
                    $error_count++;
                    continue;
                }
                
                // Build API request for this student
                $api_request = self::build_api_request($session_data, $student_data, $course_data);
                
                // Make API call
                $api_response = self::make_api_call($api_request);
                
                // Store session
                $session_id = self::store_session($session_data, $student_data, $api_response);
                
                $results[] = [
                    'student_id' => $student_id,
                    'student_email' => $student_data['email'],
                    'session_id' => $session_id,
                    'status' => 'success'
                ];
                
                $success_count++;
                
                vedmg_log_info('SCHEDULE_LAB', "Group session created for student: {$student_data['email']}");
                
            } catch (Exception $e) {
                vedmg_log_error('SCHEDULE_LAB', 'Group session failed for student', $student_id . ': ' . $e->getMessage());
                
                $results[] = [
                    'student_id' => $student_id,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                
                $error_count++;
            }
        }
        
        return [
            'type' => 'group',
            'sessions_created' => $success_count,
            'students_notified' => $success_count,
            'errors' => $error_count,
            'total_students' => count($student_ids),
            'affected_students' => $student_ids, // Add this for frontend
            'results' => $results
        ];
    }
    
    /**
     * Handle class-wide session (all students in course)
     */
    private static function handle_class_wide_session($session_data) {
        vedmg_log_info('SCHEDULE_LAB', 'Processing class-wide session');
        
        // Get all enrolled students for this course
        $enrolled_students = self::get_course_enrolled_students($session_data['course_id']);
        
        if (empty($enrolled_students)) {
            throw new Exception('No enrolled students found for course: ' . $session_data['course_id']);
        }
        
        $course_data = self::get_course_data($session_data['course_id']);
        
        $results = [];
        $success_count = 0;
        $error_count = 0;
        
        foreach ($enrolled_students as $student_data) {
            try {
                // Build API request for this student
                $api_request = self::build_api_request($session_data, $student_data, $course_data);
                
                // Make API call
                $api_response = self::make_api_call($api_request);
                
                // Store session
                $session_id = self::store_session($session_data, $student_data, $api_response);
                
                $results[] = [
                    'student_id' => $student_data['id'],
                    'student_email' => $student_data['email'],
                    'session_id' => $session_id,
                    'status' => 'success'
                ];
                
                $success_count++;
                
                vedmg_log_info('SCHEDULE_LAB', "Class-wide session created for student: {$student_data['email']}");
                
            } catch (Exception $e) {
                vedmg_log_error('SCHEDULE_LAB', 'Class-wide session failed for student', $student_data['email'] . ': ' . $e->getMessage());
                
                $results[] = [
                    'student_id' => $student_data['id'],
                    'student_email' => $student_data['email'],
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                
                $error_count++;
            }
        }
        
        // Get student IDs for affected_students
        $student_ids = array_map(function($student) {
            return $student['id'];
        }, $enrolled_students);

        return [
            'type' => 'class_wide',
            'sessions_created' => $success_count,
            'students_notified' => $success_count,
            'errors' => $error_count,
            'total_students' => count($enrolled_students),
            'affected_students' => $student_ids, // Add this for frontend
            'results' => $results
        ];
    }
    
    /**
     * Build API request data (based on calender_api.txt documentation)
     */
    private static function build_api_request($session_data, $student_data, $course_data) {
        // Build description with meeting link
        $description = $session_data['description'];
        if (!empty($course_data['meeting_link'])) {
            $description .= '. Please join at ' . $course_data['meeting_link'];
        }

        // Build datetime strings - fix for single day sessions
        // Don't add timezone to match working API format
        $start_datetime = $session_data['session_date'] . 'T' . $session_data['start_time'] . ':00';

        // For single day sessions (not recurring), use same date with proper end time
        if (!isset($session_data['is_recurring']) || !$session_data['is_recurring']) {
            // Single session: use same date for both start and end, just different times
            $end_datetime = $session_data['session_date'] . 'T' . $session_data['end_time'] . ':00';
        } else {
            // Recurring session: use the provided end date
            $end_datetime = $session_data['end_date'] . 'T' . $session_data['end_time'] . ':00';
        }

        // Build frequency string
        $frequency_string = self::build_frequency_string($session_data);

        // Use the actual calendar_id from database (not the google_classroom_id)
        $calendar_id = $course_data['calendar_id'];

        if (empty($calendar_id)) {
            throw new Exception("Calendar ID is empty for course: {$course_data['course_id']}");
        }

        return [
            'calendar_id' => $calendar_id,
            'user_email' => $student_data['email'],
            'summary' => $session_data['summary'],
            'location' => 'On Google Meet',
            'description' => $description,
            'start_datetime' => $start_datetime,
            'end_datetime' => $end_datetime,
            'instructor_email' => $course_data['instructor_email'],
            'meeting_frequency' => $frequency_string
        ];
    }
    
    /**
     * Build frequency string for API
     */
    private static function build_frequency_string($session_data) {
        if (!isset($session_data['is_recurring']) || !$session_data['is_recurring']) {
            // For single sessions, return a simple RRULE with COUNT=1
            return 'FREQ=WEEKLY;COUNT=1';
        }
        
        $frequency = $session_data['recurring_frequency'];
        $count = $session_data['recurring_count'];
        
        $frequency_string = 'FREQ=' . $frequency;
        
        if ($frequency === 'WEEKLY' && !empty($session_data['recurring_days'])) {
            $frequency_string .= ';BYDAY=' . implode(',', $session_data['recurring_days']);
        } elseif ($frequency === 'MONTHLY' && !empty($session_data['recurring_dates']) && !empty($session_data['recurring_month'])) {
            $frequency_string .= ';BYMONTHDAY=' . implode(',', $session_data['recurring_dates']);
            $frequency_string .= ';BYMONTH=' . $session_data['recurring_month'];
        }
        
        $frequency_string .= ';COUNT=' . $count;
        
        return $frequency_string;
    }
    
    /**
     * Make API call to Google Calendar (or mock for testing)
     */
    private static function make_api_call($request_data) {
        // Debug: Log the API request
        vedmg_log_info('SCHEDULE_LAB', 'API Request: ' . json_encode($request_data, JSON_PRETTY_PRINT));

        // For debugging: Enable test mode to avoid hitting real API
        // Set to false to make real API calls
        self::$test_mode = false;

        // Test mode - return mock response without hitting real API
        if (self::$test_mode || (defined('VEDMG_TEST_MODE') && VEDMG_TEST_MODE)) {
            vedmg_log_info('SCHEDULE_LAB', 'TEST MODE: Mock API response generated');

            return [
                'kind' => 'calendar#event',
                'id' => 'test_event_' . uniqid(),
                'status' => 'confirmed',
                'htmlLink' => 'https://calendar.google.com/calendar/event?eid=test',
                'summary' => $request_data['summary'],
                'description' => $request_data['description'],
                'start' => ['dateTime' => $request_data['start_datetime']],
                'end' => ['dateTime' => $request_data['end_datetime']],
                'attendees' => [
                    ['email' => $request_data['user_email'], 'responseStatus' => 'needsAction'],
                    ['email' => $request_data['instructor_email'], 'responseStatus' => 'accepted']
                ],
                'recurrence' => [$request_data['meeting_frequency']],
                'test_mode' => true,
                'api_request_sent' => $request_data
            ];
        }

        // Validate required fields before making API call
        $required_fields = ['calendar_id', 'user_email', 'summary', 'start_datetime', 'end_datetime', 'instructor_email'];
        foreach ($required_fields as $field) {
            if (empty($request_data[$field])) {
                throw new Exception("Missing required API field: $field");
            }
        }

        vedmg_log_info('SCHEDULE_LAB', 'Making real API call to: ' . self::$api_endpoint);

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => self::$api_endpoint,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($request_data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'x-api-key: ' . self::$api_key
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);

        curl_close($ch);

        // Debug: Log the API response
        vedmg_log_info('SCHEDULE_LAB', "API Response - HTTP: $http_code, Body: " . substr($response, 0, 500));

        if ($curl_error) {
            vedmg_log_error('SCHEDULE_LAB', 'CURL Error', $curl_error);
            throw new Exception('API call failed: ' . $curl_error);
        }

        $response_data = json_decode($response, true);

        if ($http_code !== 200) {
            vedmg_log_error('SCHEDULE_LAB', 'API Error', "HTTP $http_code: " . ($response_data['error'] ?? $response));
            throw new Exception('API returned error: ' . ($response_data['error'] ?? 'HTTP ' . $http_code));
        }

        // Validate Google Calendar API response format (based on calender_api.txt)
        if (!isset($response_data['kind']) || $response_data['kind'] !== 'calendar#event') {
            throw new Exception('Invalid API response format: ' . json_encode($response_data));
        }

        return $response_data;
    }

    /**
     * Enable test mode (for testing without hitting real API)
     */
    public static function enable_test_mode() {
        self::$test_mode = true;
    }

    /**
     * Disable test mode
     */
    public static function disable_test_mode() {
        self::$test_mode = false;
    }

    /**
     * Get enrollment ID for student and course
     */
    private static function get_enrollment_id($student_id, $course_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vedmg_student_enrollments';

        $enrollment_id = $wpdb->get_var($wpdb->prepare(
            "SELECT enrollment_id FROM $table_name WHERE student_id = %d AND course_id = %d",
            $student_id,
            $course_id
        ));

        return $enrollment_id;
    }



    /**
     * Get student data
     */
    private static function get_student_data($student_id) {
        global $wpdb;
        
        $user = get_user_by('ID', $student_id);
        if (!$user) {
            return false;
        }
        
        return [
            'id' => $user->ID,
            'email' => $user->user_email,
            'name' => $user->display_name
        ];
    }
    
    /**
     * Get course data
     */
    private static function get_course_data($course_id) {
        global $wpdb;

        try {
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            vedmg_log_info('SCHEDULE_LAB', "Getting course data for course_id: $course_id from table: $courses_table");

            // Check if wpdb and prepare method exist
            if (!$wpdb || !method_exists($wpdb, 'prepare')) {
                throw new Exception('WordPress database object not available or prepare method missing');
            }

            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            if (!$table_exists) {
                throw new Exception("Table $courses_table does not exist");
            }

            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            if ($wpdb->last_error) {
                throw new Exception('Database error: ' . $wpdb->last_error);
            }

            if (!$course) {
                vedmg_log_error('SCHEDULE_LAB', "Course not found with ID: $course_id");
                return false;
            }

            vedmg_log_info('SCHEDULE_LAB', "Course data retrieved successfully", [
                'course_id' => $course->course_id,
                'course_name' => $course->course_name,
                'calendar_id' => $course->calendar_id ? substr($course->calendar_id, 0, 20) . '...' : 'null',
                'meeting_link' => $course->meeting_link ? 'present' : 'null',
                'instructor_email' => $course->instructor_email
            ]);

            return [
                'course_id' => $course->course_id,
                'course_name' => $course->course_name,
                'google_classroom_id' => $course->google_classroom_id,
                'calendar_id' => $course->calendar_id,
                'meeting_link' => $course->meeting_link,
                'instructor_email' => $course->instructor_email
            ];

        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Error getting course data', [
                'course_id' => $course_id,
                'error' => $e->getMessage(),
                'wpdb_available' => isset($wpdb) ? 'yes' : 'no',
                'wpdb_class' => isset($wpdb) ? get_class($wpdb) : 'not set'
            ]);
            throw $e;
        }
    }
    
    /**
     * Get all enrolled students for a course
     */
    private static function get_course_enrolled_students($course_id) {
        global $wpdb;
        
        $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
        
        $student_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT student_id FROM $enrollments_table WHERE course_id = %d",
            $course_id
        ));
        
        $students = [];
        foreach ($student_ids as $student_id) {
            $student_data = self::get_student_data($student_id);
            if ($student_data) {
                $students[] = $student_data;
            }
        }
        
        return $students;
    }
    
    /**
     * Store session in database
     */
    private static function store_session($session_data, $student_data, $api_response) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';

        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            if (!$table_exists) {
                vedmg_log_error('SCHEDULE_LAB', 'Table does not exist', $sessions_table);
                throw new Exception("Table $sessions_table does not exist");
            }

            // Prepare data for direct database insertion
            $insert_data = [
                'course_id' => intval($session_data['course_id']),
                'google_classroom_id' => '', // Will be filled from course data if needed
                'session_title' => sanitize_text_field($session_data['summary']),
                'session_description' => sanitize_textarea_field($session_data['description']),
                'google_meet_link' => '', // Will be filled from API response if needed
                'scheduled_date' => sanitize_text_field($session_data['session_date']),
                'start_time' => sanitize_text_field($session_data['start_time']),
                'end_time' => sanitize_text_field($session_data['end_time']),
                'session_status' => 'scheduled',
                'attendance_required' => 1,
                'max_participants' => null,
                'created_date' => current_time('mysql'),
                'updated_date' => current_time('mysql')
            ];

            // Insert session into database
            $result = $wpdb->insert($sessions_table, $insert_data);

            if ($result === false) {
                vedmg_log_error('SCHEDULE_LAB', 'Failed to insert session into database', $wpdb->last_error);
                throw new Exception('Database insertion failed: ' . $wpdb->last_error);
            }

            $session_id = $wpdb->insert_id;
            vedmg_log_info('SCHEDULE_LAB', "Session stored successfully with ID: $session_id");

            return $session_id;

        } catch (Exception $e) {
            vedmg_log_error('SCHEDULE_LAB', 'Error storing session in database', $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Sanitize session data
     */
    private static function sanitize_session_data($post_data) {
        // Handle student_ids from JSON string (for group sessions)
        $student_ids = [];
        if (isset($post_data['student_ids'])) {
            if (is_string($post_data['student_ids'])) {
                $decoded = json_decode($post_data['student_ids'], true);
                if (is_array($decoded)) {
                    $student_ids = array_map('intval', $decoded);
                }
            } elseif (is_array($post_data['student_ids'])) {
                $student_ids = array_map('intval', $post_data['student_ids']);
            }
        }

        return [
            'student_id' => isset($post_data['student_id']) ? intval($post_data['student_id']) : null,
            'student_ids' => $student_ids,
            'course_id' => intval($post_data['course_id']),
            'summary' => sanitize_text_field($post_data['summary'] ?? $post_data['session_title'] ?? ''),
            'description' => sanitize_textarea_field($post_data['description'] ?? $post_data['session_description'] ?? ''),
            'session_date' => sanitize_text_field($post_data['session_date']),
            'end_date' => sanitize_text_field($post_data['end_date'] ?? $post_data['session_date']), // Default to session_date if not provided
            'start_time' => sanitize_text_field($post_data['start_time']),
            'end_time' => sanitize_text_field($post_data['end_time']),
            'timezone' => sanitize_text_field($post_data['timezone']),
            'is_recurring' => isset($post_data['is_recurring']) ? true : false,
            'recurring_frequency' => sanitize_text_field($post_data['recurring_frequency'] ?? ''),
            'recurring_count' => intval($post_data['recurring_count'] ?? 1),
            'recurring_days' => isset($post_data['recurring_days']) ? array_map('sanitize_text_field', $post_data['recurring_days']) : [],
            'recurring_dates' => isset($post_data['recurring_dates']) ? array_map('sanitize_text_field', $post_data['recurring_dates']) : [],
            'recurring_month' => sanitize_text_field($post_data['recurring_month'] ?? '')
        ];
    }
}

// Register AJAX handlers (only if WordPress is loaded)
if (function_exists('add_action')) {
    add_action('wp_ajax_vedmg_schedule_lab', ['VedMG_Schedule_Lab_Handler', 'handle_schedule_request']);
    add_action('wp_ajax_nopriv_vedmg_schedule_lab', ['VedMG_Schedule_Lab_Handler', 'handle_schedule_request']);

    // Schedule lab AJAX endpoints registered

    // AJAX handlers registered
}

?>
