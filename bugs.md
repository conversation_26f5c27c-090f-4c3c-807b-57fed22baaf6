currently there is three biggest bugs in the schedule lab modal. 

1. in the modal need to add the end date for the completion of the api 
2. fetch the students email id from the database or elsewhare aldn detect that which student's schedule lab button is clicked and then fetch that email
3. if that is group session fetch all the emails  
4. if that is class wise then fetch all the sessions
5. for the course details see the database the course details are mentioned or see which student is enrolled in which classroom or purchased which course
6. based on that course fetch the instructor details and meeting link from the vedmg_classroom table from database 


and ocne every detail is gor hit the api the api details are 
link : https://gclassroom-839391304260.us-central1.run.app/share_invite

body : {"calendar_id": "<EMAIL>", "user_email": "<EMAIL>", "summary": "live presentation", "location": "On Google Meet", "description": "Initial meeting to kickoff the new project.Please join ath https://meet.google.com/fqs-wgeg-ixt", "start_datetime": "2025-08-28T22:30:00+05:30", "end_datetime": "2025-08-28T23:00:00+05:30", "instructor_email": "<EMAIL>", "meeting_frequency": "FREQ=WEEKLY;BYDAY=MO,TU,WE;COUNT=3"}	

header : x-api-key : G$$gle@VedMG!@#



in the @c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom/bugs.md i mentioned the bugs. 
the flow should be : instructor creating course -> automatically classroom gets created (working)
atudent purchase the course -> automatically calander gets shared and all the data stored in te database table (working)

now thierd flow 
teacher will accept the classroom and generate a meeting link and then paste in the generate meeting link modal of the course management page of the admin pannel -> and in database it will get stored -> after that the teacher will go to student enrollments page and click on the schedule lab -> for those student who purchased the course -> after that in that modal admin/instructor will enter all the details in the modal and when clicked on the schedule session button the api should get hit and a invite should shared to the student. 
the third flow is partially working till the database storing of the meeting link and in the student enrollment page nothing working i created a file in api folder to handle specific this task and it works but the data is not being sent to the api properly