uter [2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] Invoking VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 03:30:01] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 03:30:01] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 03:30:01] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 03:30:01] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 03:30:01] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 03:30:01] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 03:30:01] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 03:30:01] [INFO] [DATABASE] Created session tracking table
[2025-09-06 03:30:01] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 03:30:01] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 03:30:01] [INFO] [DATABASE] Database upgrade completed successfully to version 2.0
[2025-09-06 03:30:01] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] Column already exists: is_featured
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] Column already exists: featured_date
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] SUCCESS: Both is_featured and featured_date columns are present.
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.0
[2025-09-06 03:30:01] [INFO] [CLI-UPGRADE] --- Script completed ---
[2025-09-06 03:31:09] [INFO] [API] API handlers initialized
[2025-09-06 03:31:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 03:31:09] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 03:31:09] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 03:31:09] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 03:31:09] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 03:31:09] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 03:31:09] [INFO] [CORE] Starting core initialization
[2025-09-06 03:31:09] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 03:31:09] [INFO] [CORE] Admin functionality loaded
[2025-09-06 03:31:09] [INFO] [CORE] Core initialization completed
[2025-09-06 03:31:10] [INFO] [API] API handlers initialized
[2025-09-06 03:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 03:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 03:31:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 03:31:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 03:31:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 03:31:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 03:31:10] [INFO] [CORE] Starting core initialization
[2025-09-06 03:31:10] [INFO] [CORE] Core initialization completed
[2025-09-06 03:31:11] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:50:28] [INFO] [API] API handlers initialized
[2025-09-06 05:50:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:50:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:50:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:50:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:50:29] [INFO] [CORE] Starting core initialization
[2025-09-06 05:50:29] [INFO] [CORE] Core initialization completed
[2025-09-06 05:50:30] [INFO] [API] API handlers initialized
[2025-09-06 05:50:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:50:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:50:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:50:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:50:30] [INFO] [CORE] Starting core initialization
[2025-09-06 05:50:30] [INFO] [CORE] Core initialization completed
[2025-09-06 05:50:42] [INFO] [API] API handlers initialized
[2025-09-06 05:50:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:42] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:50:42] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:50:42] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:50:42] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:50:42] [INFO] [CORE] Starting core initialization
[2025-09-06 05:50:42] [INFO] [CORE] Core initialization completed
[2025-09-06 05:50:44] [INFO] [API] API handlers initialized
[2025-09-06 05:50:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:45] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:50:45] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:50:45] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:50:45] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:50:45] [INFO] [CORE] Starting core initialization
[2025-09-06 05:50:45] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:50:45] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:50:45] [INFO] [CORE] Core initialization completed
[2025-09-06 05:50:45] [INFO] [ADMIN] Admin menu created
[2025-09-06 05:50:46] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:50:51] [INFO] [API] API handlers initialized
[2025-09-06 05:50:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:50:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:50:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:50:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:50:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:50:51] [INFO] [CORE] Starting core initialization
[2025-09-06 05:50:51] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:50:51] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:50:51] [INFO] [CORE] Core initialization completed
[2025-09-06 05:50:52] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:51:51] [INFO] [API] API handlers initialized
[2025-09-06 05:51:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:51:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:51:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:51:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:51:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:51:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:51:51] [INFO] [CORE] Starting core initialization
[2025-09-06 05:51:51] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:51:51] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:51:51] [INFO] [CORE] Core initialization completed
[2025-09-06 05:51:52] [INFO] [API] API handlers initialized
[2025-09-06 05:51:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:51:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:51:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:51:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:51:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:51:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:51:52] [INFO] [CORE] Starting core initialization
[2025-09-06 05:51:52] [INFO] [CORE] Core initialization completed
[2025-09-06 05:51:53] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:53:52] [INFO] [API] API handlers initialized
[2025-09-06 05:53:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:53:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:53:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:53:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:53:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:53:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:53:52] [INFO] [CORE] Starting core initialization
[2025-09-06 05:53:52] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:53:52] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:53:52] [INFO] [CORE] Core initialization completed
[2025-09-06 05:53:53] [INFO] [API] API handlers initialized
[2025-09-06 05:53:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:53:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:53:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:53:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:53:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:53:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:53:53] [INFO] [CORE] Starting core initialization
[2025-09-06 05:53:53] [INFO] [CORE] Core initialization completed
[2025-09-06 05:53:54] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:54:52] [INFO] [API] API handlers initialized
[2025-09-06 05:54:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:54:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:54:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:54:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:54:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:54:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:54:53] [INFO] [CORE] Starting core initialization
[2025-09-06 05:54:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:54:53] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:54:53] [INFO] [CORE] Core initialization completed
[2025-09-06 05:54:54] [INFO] [API] API handlers initialized
[2025-09-06 05:54:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:54:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:54:54] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:54:54] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:54:54] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:54:54] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:54:54] [INFO] [CORE] Starting core initialization
[2025-09-06 05:54:54] [INFO] [CORE] Core initialization completed
[2025-09-06 05:54:55] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:56:52] [INFO] [API] API handlers initialized
[2025-09-06 05:56:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:56:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:56:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:56:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:56:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:56:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:56:53] [INFO] [CORE] Starting core initialization
[2025-09-06 05:56:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:56:53] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:56:53] [INFO] [CORE] Core initialization completed
[2025-09-06 05:56:54] [INFO] [API] API handlers initialized
[2025-09-06 05:56:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:56:55] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:56:55] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:56:55] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:56:55] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:56:55] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:56:55] [INFO] [CORE] Starting core initialization
[2025-09-06 05:56:55] [INFO] [CORE] Core initialization completed
[2025-09-06 05:56:55] [INFO] [ADMIN] Settings initialized
[2025-09-06 05:58:53] [INFO] [API] API handlers initialized
[2025-09-06 05:58:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:58:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:58:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:58:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:58:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:58:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:58:53] [INFO] [CORE] Starting core initialization
[2025-09-06 05:58:53] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 05:58:53] [INFO] [CORE] Admin functionality loaded
[2025-09-06 05:58:53] [INFO] [CORE] Core initialization completed
[2025-09-06 05:58:54] [INFO] [API] API handlers initialized
[2025-09-06 05:58:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:58:54] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 05:58:54] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 05:58:54] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 05:58:54] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 05:58:54] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 05:58:54] [INFO] [CORE] Starting core initialization
[2025-09-06 05:58:54] [INFO] [CORE] Core initialization completed
[2025-09-06 05:58:55] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:00:21] [INFO] [API] API handlers initialized
[2025-09-06 06:00:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:00:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:00:21] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:00:21] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:00:21] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:00:21] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:00:21] [INFO] [CORE] Starting core initialization
[2025-09-06 06:00:21] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:00:21] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:00:21] [INFO] [CORE] Core initialization completed
[2025-09-06 06:00:22] [INFO] [API] API handlers initialized
[2025-09-06 06:00:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:00:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:00:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:00:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:00:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:00:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:00:23] [INFO] [CORE] Starting core initialization
[2025-09-06 06:00:23] [INFO] [CORE] Core initialization completed
[2025-09-06 06:00:23] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:02:22] [INFO] [API] API handlers initialized
[2025-09-06 06:02:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:02:22] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:02:22] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:02:22] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:02:22] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:02:22] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:02:22] [INFO] [DATABASE] Starting database upgrade from version 2.0
[2025-09-06 06:06:24] [INFO] [API] API handlers initialized
[2025-09-06 06:06:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:06:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:06:24] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:06:24] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:06:24] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:06:24] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:06:24] [INFO] [DATABASE] Starting database upgrade from version 2.0
[2025-09-06 06:06:24] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:06:24] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:06:24] [INFO] [CORE] Starting core initialization
[2025-09-06 06:06:24] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:06:24] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:06:24] [INFO] [CORE] Core initialization completed
[2025-09-06 06:06:26] [INFO] [API] API handlers initialized
[2025-09-06 06:06:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:06:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:06:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:06:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:06:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:06:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:06:26] [INFO] [CORE] Starting core initialization
[2025-09-06 06:06:26] [INFO] [CORE] Core initialization completed
[2025-09-06 06:06:26] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:08:25] [INFO] [API] API handlers initialized
[2025-09-06 06:08:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:08:25] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:08:25] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:08:25] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:08:25] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:08:25] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:08:25] [INFO] [CORE] Starting core initialization
[2025-09-06 06:08:25] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:08:25] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:08:25] [INFO] [CORE] Core initialization completed
[2025-09-06 06:08:26] [INFO] [API] API handlers initialized
[2025-09-06 06:08:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:08:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:08:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:08:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:08:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:08:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:08:26] [INFO] [CORE] Starting core initialization
[2025-09-06 06:08:26] [INFO] [CORE] Core initialization completed
[2025-09-06 06:08:27] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:10:26] [INFO] [API] API handlers initialized
[2025-09-06 06:10:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:10:26] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:10:26] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:10:26] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:10:26] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:10:26] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:10:26] [INFO] [CORE] Starting core initialization
[2025-09-06 06:10:26] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:10:26] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:10:26] [INFO] [CORE] Core initialization completed
[2025-09-06 06:10:28] [INFO] [API] API handlers initialized
[2025-09-06 06:10:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:10:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:10:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:10:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:10:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:10:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:10:28] [INFO] [CORE] Starting core initialization
[2025-09-06 06:10:28] [INFO] [CORE] Core initialization completed
[2025-09-06 06:10:29] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:32:27] [INFO] [API] API handlers initialized
[2025-09-06 06:32:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:32:28] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:32:28] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:32:28] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:32:28] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:32:28] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:32:28] [INFO] [CORE] Starting core initialization
[2025-09-06 06:32:28] [INFO] [CORE] Core initialization completed
[2025-09-06 06:32:29] [INFO] [API] API handlers initialized
[2025-09-06 06:32:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:32:29] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:32:29] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:32:29] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:32:29] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:32:29] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:32:29] [INFO] [CORE] Starting core initialization
[2025-09-06 06:32:29] [INFO] [CORE] Core initialization completed
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] --- VedMG ClassRoom: Force DB Upgrade Script ---
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Invoking VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 06:32:30] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 06:32:30] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 06:32:30] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 06:32:30] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 06:32:30] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 06:32:30] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 06:32:30] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 06:32:30] [INFO] [DATABASE] Created session tracking table
[2025-09-06 06:32:30] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 06:32:30] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:32:30] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:32:30] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] All essential tables exist.
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Verifying vedmg_class_sessions table columns...
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] All expected columns are present in vedmg_class_sessions table.
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Verifying vedmg_courses table columns...
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Column exists in courses table: instructor_name
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Column exists in courses table: class_join_link
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Column exists in courses table: meeting_link
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Checking for upgrade-created tables...
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Upgrade table exists: vedmg_student_classroom_mappings
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Upgrade table exists: vedmg_session_tracking
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.1
[2025-09-06 06:32:30] [INFO] [CLI-UPGRADE] --- Script completed ---
[2025-09-06 06:34:46] [INFO] [API] API handlers initialized
[2025-09-06 06:34:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:34:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:34:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:34:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:34:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:34:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:34:46] [INFO] [CORE] Starting core initialization
[2025-09-06 06:34:46] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:34:46] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:34:46] [INFO] [CORE] Core initialization completed
[2025-09-06 06:34:47] [INFO] [API] API handlers initialized
[2025-09-06 06:34:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:34:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:34:47] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:34:47] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:34:47] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:34:47] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:34:47] [INFO] [CORE] Starting core initialization
[2025-09-06 06:34:47] [INFO] [CORE] Core initialization completed
[2025-09-06 06:34:48] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:35:44] [INFO] [API] API handlers initialized
[2025-09-06 06:35:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:35:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:35:44] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:35:44] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:35:44] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:35:44] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:35:44] [INFO] [CORE] Starting core initialization
[2025-09-06 06:35:44] [INFO] [CORE] Core initialization completed
[2025-09-06 06:35:45] [INFO] [API] API handlers initialized
[2025-09-06 06:35:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:35:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:35:45] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:35:45] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:35:45] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:35:45] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:35:45] [INFO] [CORE] Starting core initialization
[2025-09-06 06:35:45] [INFO] [CORE] Core initialization completed
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] --- VedMG ClassRoom: Force DB Upgrade Script ---
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] Invoking VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 06:35:46] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 06:35:46] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 06:35:46] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 06:35:46] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 06:35:46] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 06:35:46] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 06:35:46] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 06:35:46] [INFO] [DATABASE] Created session tracking table
[2025-09-06 06:35:46] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 06:35:46] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:35:46] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:35:46] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] Column already exists: is_featured
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] Column already exists: featured_date
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] SUCCESS: Both is_featured and featured_date columns are present.
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.1
[2025-09-06 06:35:46] [INFO] [CLI-UPGRADE] --- Script completed ---
[2025-09-06 06:36:47] [INFO] [API] API handlers initialized
[2025-09-06 06:36:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:47] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:36:47] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:36:47] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:36:47] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:36:47] [INFO] [CORE] Starting core initialization
[2025-09-06 06:36:47] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:36:47] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:36:47] [INFO] [CORE] Core initialization completed
[2025-09-06 06:36:48] [INFO] [API] API handlers initialized
[2025-09-06 06:36:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:36:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:36:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:36:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:36:48] [INFO] [CORE] Starting core initialization
[2025-09-06 06:36:48] [INFO] [CORE] Core initialization completed
[2025-09-06 06:36:48] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:36:52] [INFO] [API] API handlers initialized
[2025-09-06 06:36:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:36:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:36:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:36:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:36:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:36:52] [INFO] [CORE] Starting core initialization
[2025-09-06 06:36:52] [INFO] [CORE] Core initialization completed
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] --- VedMG ClassRoom: Force DB Upgrade Script ---
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Invoking VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 06:36:53] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 06:36:53] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 06:36:53] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 06:36:53] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 06:36:53] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 06:36:53] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 06:36:53] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 06:36:53] [INFO] [DATABASE] Created session tracking table
[2025-09-06 06:36:53] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 06:36:53] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:36:53] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:36:53] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] All essential tables exist.
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Verifying vedmg_class_sessions table columns...
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] All expected columns are present in vedmg_class_sessions table.
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Verifying vedmg_courses table columns...
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Column exists in courses table: instructor_name
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Column exists in courses table: class_join_link
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Column exists in courses table: meeting_link
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Checking for upgrade-created tables...
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Upgrade table exists: vedmg_student_classroom_mappings
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Upgrade table exists: vedmg_session_tracking
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.1
[2025-09-06 06:36:53] [INFO] [CLI-UPGRADE] --- Script completed ---
[2025-09-06 06:38:48] [INFO] [API] API handlers initialized
[2025-09-06 06:38:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:38:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:38:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:38:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:38:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:38:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:38:48] [INFO] [CORE] Starting core initialization
[2025-09-06 06:38:48] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:38:48] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:38:48] [INFO] [CORE] Core initialization completed
[2025-09-06 06:38:49] [INFO] [API] API handlers initialized
[2025-09-06 06:38:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:38:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:38:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:38:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:38:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:38:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:38:49] [INFO] [CORE] Starting core initialization
[2025-09-06 06:38:49] [INFO] [CORE] Core initialization completed
[2025-09-06 06:38:50] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:39:13] [INFO] [API] API handlers initialized
[2025-09-06 06:39:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:39:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:39:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:39:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:39:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:39:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:39:14] [INFO] [CORE] Starting core initialization
[2025-09-06 06:39:14] [INFO] [CORE] Core initialization completed
[2025-09-06 06:39:15] [INFO] [API] API handlers initialized
[2025-09-06 06:39:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:39:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:39:15] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:39:15] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:39:15] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:39:15] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:39:15] [INFO] [CORE] Starting core initialization
[2025-09-06 06:39:15] [INFO] [CORE] Core initialization completed
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] === VedMG ClassRoom: Comprehensive DB Upgrade Script ===
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Step 1: Running VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 06:39:16] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 06:39:16] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 06:39:16] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 06:39:16] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 06:39:16] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 06:39:16] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 06:39:16] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 06:39:16] [INFO] [DATABASE] Created session tracking table
[2025-09-06 06:39:16] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 06:39:16] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:39:16] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:39:16] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Step 2: Verifying essential tables exist...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_courses
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_student_enrollments
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_class_sessions
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_instructor_sync
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Step 3: Verifying all required columns exist...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Checking vedmg_class_sessions columns...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.assigned_instructor_id
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.is_featured
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.featured_date
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.session_type
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.duration_minutes
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.is_recurring
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_pattern
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_days
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_dates
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_count
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_end_date
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.selected_student_ids
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.meeting_type
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.target_student_id
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ session_status enum includes 'google classroom'
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Checking vedmg_student_enrollments columns...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.instructor_name
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.last_scheduled_date
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.total_sessions_scheduled
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.last_session_type
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ enrollment_status column properly removed
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Checking vedmg_courses columns...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.instructor_name
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.class_join_link
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.meeting_link
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Checking additional tables...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Additional table exists: wp_vedmg_student_classroom_mappings (v1.3)
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Additional table exists: wp_vedmg_session_tracking (v2.0)
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Step 4: Final verification...
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.1
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ Database is at latest version (2.1)
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] ✓ All critical columns for admin pages are present
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] === Script completed ===
[2025-09-06 06:39:16] [INFO] [CLI-UPGRADE] STATUS: SUCCESS
[2025-09-06 06:40:49] [INFO] [API] API handlers initialized
[2025-09-06 06:40:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:40:49] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:40:49] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:40:49] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:40:49] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:40:49] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:40:49] [INFO] [CORE] Starting core initialization
[2025-09-06 06:40:49] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:40:49] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:40:49] [INFO] [CORE] Core initialization completed
[2025-09-06 06:40:50] [INFO] [API] API handlers initialized
[2025-09-06 06:40:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:40:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:40:50] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:40:50] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:40:50] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:40:50] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:40:50] [INFO] [CORE] Starting core initialization
[2025-09-06 06:40:50] [INFO] [CORE] Core initialization completed
[2025-09-06 06:40:51] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:42:50] [INFO] [API] API handlers initialized
[2025-09-06 06:42:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:42:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:42:50] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:42:50] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:42:50] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:42:50] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:42:50] [INFO] [CORE] Starting core initialization
[2025-09-06 06:42:50] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:42:50] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:42:50] [INFO] [CORE] Core initialization completed
[2025-09-06 06:42:51] [INFO] [API] API handlers initialized
[2025-09-06 06:42:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:42:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:42:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:42:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:42:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:42:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:42:51] [INFO] [CORE] Starting core initialization
[2025-09-06 06:42:51] [INFO] [CORE] Core initialization completed
[2025-09-06 06:42:52] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:44:51] [INFO] [API] API handlers initialized
[2025-09-06 06:44:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:44:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:44:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:44:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:44:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:44:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:44:51] [INFO] [CORE] Starting core initialization
[2025-09-06 06:44:51] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:44:51] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:44:51] [INFO] [CORE] Core initialization completed
[2025-09-06 06:44:52] [INFO] [API] API handlers initialized
[2025-09-06 06:44:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:44:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:44:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:44:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:44:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:44:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:44:52] [INFO] [CORE] Starting core initialization
[2025-09-06 06:44:52] [INFO] [CORE] Core initialization completed
[2025-09-06 06:44:53] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:45:18] [INFO] [API] API handlers initialized
[2025-09-06 06:45:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:45:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:45:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:45:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:45:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:45:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:45:19] [INFO] [CORE] Starting core initialization
[2025-09-06 06:45:19] [INFO] [CORE] Core initialization completed
[2025-09-06 06:45:19] [INFO] [API] API handlers initialized
[2025-09-06 06:45:20] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:45:20] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:45:20] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:45:20] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:45:20] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:45:20] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:45:20] [INFO] [CORE] Starting core initialization
[2025-09-06 06:45:20] [INFO] [CORE] Core initialization completed
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] === VedMG ClassRoom: Comprehensive DB Upgrade Script ===
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] Step 1: Running VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...
[2025-09-06 06:45:20] [INFO] [DATABASE] FORCE UPGRADE: Starting forced database upgrade
[2025-09-06 06:45:20] [INFO] [DATABASE] FORCE UPGRADE: Current version detected as 2.1
[2025-09-06 06:45:20] [INFO] [DATABASE] Starting database upgrade from version 1.0
[2025-09-06 06:45:20] [INFO] [DATABASE] Populated instructor names for existing courses
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.1 completed
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.2 completed
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.3 completed successfully
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.4 completed successfully
[2025-09-06 06:45:20] [DATABASE] [DB] Database ALTER on table: vedmg_class_sessions | Details: Updated session_status enum to include google classroom
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.5 completed successfully
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 1.6 completed successfully
[2025-09-06 06:45:20] [INFO] [DATABASE] Starting upgrade to version 2.0 - Enrollments Enhancement
[2025-09-06 06:45:20] [INFO] [DATABASE] Enrollment data backed up to wp_vedmg_student_enrollments_backup_v1
[2025-09-06 06:45:20] [INFO] [DATABASE] Created session tracking table
[2025-09-06 06:45:20] [INFO] [DATABASE] Populated instructor names for 0 enrollment records
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 2.0 completed successfully
[2025-09-06 06:45:20] [INFO] [DATABASE] Upgrade to version 2.1 completed successfully
[2025-09-06 06:45:20] [INFO] [DATABASE] Database upgrade completed successfully to version 2.1
[2025-09-06 06:45:20] [INFO] [DATABASE] FORCE UPGRADE: Completed successfully
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] force_upgrade() returned: true
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] Step 2: Verifying essential tables exist...
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_courses
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_student_enrollments
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_class_sessions
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Table exists: wp_vedmg_instructor_sync
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] Step 3: Verifying all required columns exist...
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] Checking vedmg_class_sessions columns...
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.assigned_instructor_id
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.is_featured
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.featured_date
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.session_type
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.duration_minutes
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.is_recurring
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_pattern
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_days
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_dates
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_count
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.recurring_end_date
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.selected_student_ids
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.meeting_type
[2025-09-06 06:45:20] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_class_sessions.target_student_id
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ session_status enum includes 'google classroom'
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] Checking vedmg_student_enrollments columns...
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.instructor_name
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.last_scheduled_date
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.total_sessions_scheduled
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_student_enrollments.last_session_type
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ enrollment_status column properly removed
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] Checking vedmg_courses columns...
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.instructor_name
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.class_join_link
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Column exists: wp_vedmg_courses.meeting_link
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] Checking additional tables...
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Additional table exists: wp_vedmg_student_classroom_mappings (v1.3)
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Additional table exists: wp_vedmg_session_tracking (v2.0)
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] Step 4: Final verification...
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] Current vedmg_classroom_db_version: 2.1
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ Database is at latest version (2.1)
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] ✓ All critical columns for admin pages are present
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] === Script completed ===
[2025-09-06 06:45:21] [INFO] [CLI-UPGRADE] STATUS: SUCCESS
[2025-09-06 06:48:45] [INFO] [API] API handlers initialized
[2025-09-06 06:48:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:45] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:48:45] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:48:45] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:48:45] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:48:45] [INFO] [CORE] Starting core initialization
[2025-09-06 06:48:45] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:48:45] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:48:45] [INFO] [CORE] Core initialization completed
[2025-09-06 06:48:46] [INFO] [API] API handlers initialized
[2025-09-06 06:48:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:48:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:48:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:48:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:48:46] [INFO] [CORE] Starting core initialization
[2025-09-06 06:48:46] [INFO] [CORE] Core initialization completed
[2025-09-06 06:48:47] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:48:47] [INFO] [API] API handlers initialized
[2025-09-06 06:48:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:47] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:48:47] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:48:47] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:48:47] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:48:47] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:48:47] [INFO] [CORE] Starting core initialization
[2025-09-06 06:48:47] [INFO] [CORE] Core initialization completed
[2025-09-06 06:50:45] [INFO] [API] API handlers initialized
[2025-09-06 06:50:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:50:45] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:50:45] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:50:45] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:50:45] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:50:45] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:50:45] [INFO] [CORE] Starting core initialization
[2025-09-06 06:50:45] [INFO] [ADMIN] Admin interface initialized
[2025-09-06 06:50:45] [INFO] [CORE] Admin functionality loaded
[2025-09-06 06:50:45] [INFO] [CORE] Core initialization completed
[2025-09-06 06:50:46] [INFO] [API] API handlers initialized
[2025-09-06 06:50:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:50:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:50:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:50:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:50:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:50:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:50:46] [INFO] [CORE] Starting core initialization
[2025-09-06 06:50:46] [INFO] [CORE] Core initialization completed
[2025-09-06 06:50:47] [INFO] [ADMIN] Settings initialized
[2025-09-06 06:51:30] [INFO] [API] API handlers initialized
[2025-09-06 06:51:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:51:30] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:51:30] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:51:30] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:51:30] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:51:30] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:51:30] [INFO] [CORE] Starting core initialization
[2025-09-06 06:51:30] [INFO] [CORE] Core initialization completed
[2025-09-06 06:51:31] [INFO] [API] API handlers initialized
[2025-09-06 06:51:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:51:31] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 06:51:31] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 06:51:31] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 06:51:31] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 06:51:31] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 06:51:31] [INFO] [CORE] Starting core initialization
[2025-09-06 06:51:31] [INFO] [CORE] Core initialization completed
[2025-09-06 09:23:36] [INFO] [API] API handlers initialized
[2025-09-06 09:24:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:24:21] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:24:21] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:24:21] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:24:21] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:24:21] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:24:21] [INFO] [CORE] Starting core initialization
[2025-09-06 09:24:21] [INFO] [CORE] Core initialization completed
[2025-09-06 09:25:14] [INFO] [API] API handlers initialized
[2025-09-06 09:25:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:25:15] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:25:15] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:25:15] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:25:15] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:25:15] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:25:15] [INFO] [CORE] Starting core initialization
[2025-09-06 09:25:15] [INFO] [CORE] Core initialization completed
[2025-09-06 09:27:16] [INFO] [API] API handlers initialized
[2025-09-06 09:27:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:27:16] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:27:16] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:27:16] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:27:16] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:27:16] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:27:16] [INFO] [CORE] Starting core initialization
[2025-09-06 09:27:16] [INFO] [CORE] Core initialization completed
[2025-09-06 09:27:17] [INFO] [API] API handlers initialized
[2025-09-06 09:27:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:27:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:27:17] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:27:17] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:27:17] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:27:17] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:27:17] [INFO] [CORE] Starting core initialization
[2025-09-06 09:27:17] [INFO] [CORE] Core initialization completed
[2025-09-06 09:31:18] [INFO] [API] API handlers initialized
[2025-09-06 09:31:18] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:31:18] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:31:18] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:31:18] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:31:18] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:31:18] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:31:18] [INFO] [CORE] Starting core initialization
[2025-09-06 09:31:18] [INFO] [CORE] Core initialization completed
[2025-09-06 09:31:19] [INFO] [API] API handlers initialized
[2025-09-06 09:31:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:31:19] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:31:19] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:31:19] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:31:19] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:31:19] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:31:19] [INFO] [CORE] Starting core initialization
[2025-09-06 09:31:19] [INFO] [CORE] Core initialization completed
[2025-09-06 09:58:42] [INFO] [API] API handlers initialized
[2025-09-06 09:59:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:59:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:59:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:59:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:59:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:59:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:59:04] [INFO] [CORE] Starting core initialization
[2025-09-06 09:59:04] [INFO] [CORE] Core initialization completed
[2025-09-06 09:59:44] [INFO] [API] API handlers initialized
[2025-09-06 09:59:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:59:44] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 09:59:44] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 09:59:44] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 09:59:44] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 09:59:44] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 09:59:44] [INFO] [CORE] Starting core initialization
[2025-09-06 09:59:44] [INFO] [CORE] Core initialization completed
[2025-09-06 11:28:29] [INFO] [API] API handlers initialized
[2025-09-06 11:28:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 11:28:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 11:28:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 11:28:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 11:28:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 11:28:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 11:28:51] [INFO] [CORE] Starting core initialization
[2025-09-06 11:28:51] [INFO] [CORE] Core initialization completed
[2025-09-06 11:29:22] [INFO] [API] API handlers initialized
[2025-09-06 11:29:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 11:29:23] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-06 11:29:23] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-06 11:29:23] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-06 11:29:23] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-06 11:29:23] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-06 11:29:23] [INFO] [CORE] Starting core initialization
[2025-09-06 11:29:23] [INFO] [CORE] Core initialization completed
[2025-09-07 05:02:49] [INFO] [API] API handlers initialized
[2025-09-07 05:03:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:03:24] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:03:24] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:03:24] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:03:24] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:03:24] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:03:24] [INFO] [CORE] Starting core initialization
[2025-09-07 05:03:24] [INFO] [CORE] Core initialization completed
[2025-09-07 05:04:39] [INFO] [API] API handlers initialized
[2025-09-07 05:05:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:05:13] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:05:13] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:05:13] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:05:13] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:05:13] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:05:13] [INFO] [CORE] Starting core initialization
[2025-09-07 05:05:13] [INFO] [CORE] Core initialization completed
[2025-09-07 05:06:34] [INFO] [API] API handlers initialized
[2025-09-07 05:06:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:42] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:42] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:06:42] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:06:42] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:06:42] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:06:42] [INFO] [CORE] Starting core initialization
[2025-09-07 05:06:42] [INFO] [ADMIN] Admin interface initialized
[2025-09-07 05:06:42] [INFO] [CORE] Admin functionality loaded
[2025-09-07 05:06:42] [INFO] [CORE] Core initialization completed
[2025-09-07 05:06:48] [INFO] [API] API handlers initialized
[2025-09-07 05:06:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:06:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:06:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:06:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:06:48] [INFO] [CORE] Starting core initialization
[2025-09-07 05:06:48] [INFO] [CORE] Core initialization completed
[2025-09-07 05:06:49] [INFO] [ADMIN] Settings initialized
[2025-09-07 05:06:49] [INFO] [API] API handlers initialized
[2025-09-07 05:06:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:50] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:50] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:06:50] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:06:50] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:06:50] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:06:50] [INFO] [CORE] Starting core initialization
[2025-09-07 05:06:50] [INFO] [ADMIN] Admin interface initialized
[2025-09-07 05:06:50] [INFO] [CORE] Admin functionality loaded
[2025-09-07 05:06:50] [INFO] [CORE] Core initialization completed
[2025-09-07 05:06:51] [INFO] [API] API handlers initialized
[2025-09-07 05:06:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:51] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:06:51] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:06:51] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:06:51] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:06:51] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:06:51] [INFO] [CORE] Starting core initialization
[2025-09-07 05:06:51] [INFO] [CORE] Core initialization completed
[2025-09-07 05:06:51] [INFO] [ADMIN] Settings initialized
[2025-09-07 05:30:36] [INFO] [API] API handlers initialized
[2025-09-07 05:30:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:30:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:30:37] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:30:37] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:30:37] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:30:37] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:30:37] [INFO] [CORE] Starting core initialization
[2025-09-07 05:30:37] [INFO] [CORE] Core initialization completed
[2025-09-07 05:30:37] [INFO] [API] API handlers initialized
[2025-09-07 05:30:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:30:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-07 05:30:38] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-07 05:30:38] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-07 05:30:38] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-07 05:30:38] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-07 05:30:38] [INFO] [CORE] Starting core initialization
[2025-09-07 05:30:38] [INFO] [CORE] Core initialization completed
[2025-09-08 08:30:37] [INFO] [API] API handlers initialized
[2025-09-08 08:30:37] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:30:55] [INFO] [API] API handlers initialized
[2025-09-08 08:30:55] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:30:59] [INFO] [API] API handlers initialized
[2025-09-08 08:30:59] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:10] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:10] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:10] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:10] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:10] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:10] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:51] [INFO] [API] API handlers initialized
[2025-09-08 08:31:51] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:31:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:52] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:52] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:55] [INFO] [API] API handlers initialized
[2025-09-08 08:31:55] [INFO] [API] API handlers initialized
[2025-09-08 08:31:55] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:31:55] [INFO] [API] API handlers initialized
[2025-09-08 08:31:55] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:31:55] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:56] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:56] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:56] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:56] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:31:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:31:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:31:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:31:56] [INFO] [CORE] Starting core initialization
[2025-09-08 08:31:56] [INFO] [CORE] Core initialization completed
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:31:56] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:32:08] [INFO] [API] API handlers initialized
[2025-09-08 08:32:08] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:32:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:32:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:32:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:32:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:32:14] [INFO] [CORE] Starting core initialization
[2025-09-08 08:32:14] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:32:14] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:32:14] [INFO] [CORE] Core initialization completed
[2025-09-08 08:32:21] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:32:51] [INFO] [API] API handlers initialized
[2025-09-08 08:32:51] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:32:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:52] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:52] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:32:52] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:32:52] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:32:52] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:32:52] [INFO] [CORE] Starting core initialization
[2025-09-08 08:32:52] [INFO] [CORE] Core initialization completed
[2025-09-08 08:32:53] [INFO] [API] API handlers initialized
[2025-09-08 08:32:53] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:32:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:32:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:32:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:32:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:32:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:32:53] [INFO] [CORE] Starting core initialization
[2025-09-08 08:32:53] [INFO] [CORE] Core initialization completed
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:32:54] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:33:10] [INFO] [API] API handlers initialized
[2025-09-08 08:33:10] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:33:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:10] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:10] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:33:10] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:33:10] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:33:10] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:33:10] [INFO] [CORE] Starting core initialization
[2025-09-08 08:33:10] [INFO] [CORE] Core initialization completed
[2025-09-08 08:33:11] [INFO] [API] API handlers initialized
[2025-09-08 08:33:11] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:33:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:12] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:12] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:33:12] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:33:12] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:33:12] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:33:12] [INFO] [CORE] Starting core initialization
[2025-09-08 08:33:12] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:33:12] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:33:12] [INFO] [CORE] Core initialization completed
[2025-09-08 08:33:14] [INFO] [API] API handlers initialized
[2025-09-08 08:33:14] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:33:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:14] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:14] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:33:14] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:33:14] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:33:14] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:33:14] [INFO] [CORE] Starting core initialization
[2025-09-08 08:33:14] [INFO] [CORE] Core initialization completed
[2025-09-08 08:33:15] [INFO] [ADMIN] Admin menu created
[2025-09-08 08:33:18] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:33:32] [INFO] [API] API handlers initialized
[2025-09-08 08:33:32] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:33:32] [INFO] [API] API handlers initialized
[2025-09-08 08:33:32] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:33:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:33:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:33:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:33:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:33:32] [INFO] [CORE] Starting core initialization
[2025-09-08 08:33:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:33:32] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:33:32] [INFO] [CORE] Core initialization completed
[2025-09-08 08:33:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:33:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:33:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:33:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:33:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:33:32] [INFO] [CORE] Starting core initialization
[2025-09-08 08:33:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:33:32] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:33:32] [INFO] [CORE] Core initialization completed
[2025-09-08 08:33:33] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:33:34] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:35:32] [INFO] [API] API handlers initialized
[2025-09-08 08:35:32] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:35:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:35:32] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:35:32] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:35:32] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:35:32] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:35:32] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:35:32] [INFO] [CORE] Starting core initialization
[2025-09-08 08:35:32] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:35:32] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:35:32] [INFO] [CORE] Core initialization completed
[2025-09-08 08:35:33] [INFO] [API] API handlers initialized
[2025-09-08 08:35:33] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:35:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:35:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:35:34] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:35:34] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:35:34] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:35:34] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:35:34] [INFO] [CORE] Starting core initialization
[2025-09-08 08:35:34] [INFO] [CORE] Core initialization completed
[2025-09-08 08:35:34] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:37:34] [INFO] [API] API handlers initialized
[2025-09-08 08:37:34] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:37:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:37:35] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:37:35] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:37:35] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:37:35] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:37:35] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:37:35] [INFO] [CORE] Starting core initialization
[2025-09-08 08:37:35] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:37:35] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:37:35] [INFO] [CORE] Core initialization completed
[2025-09-08 08:37:37] [INFO] [API] API handlers initialized
[2025-09-08 08:37:37] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:37:38] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:37:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:37:38] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:37:38] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:37:38] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:37:38] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:37:38] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:37:38] [INFO] [CORE] Starting core initialization
[2025-09-08 08:37:38] [INFO] [CORE] Core initialization completed
[2025-09-08 08:39:04] [INFO] [API] API handlers initialized
[2025-09-08 08:39:04] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:39:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:04] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:04] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:39:04] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:39:04] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:39:04] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:39:04] [INFO] [CORE] Starting core initialization
[2025-09-08 08:39:04] [INFO] [CORE] Core initialization completed
[2025-09-08 08:39:05] [INFO] [API] API handlers initialized
[2025-09-08 08:39:05] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:39:05] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:05] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:05] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:39:05] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:39:05] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:39:05] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:39:05] [INFO] [CORE] Starting core initialization
[2025-09-08 08:39:05] [INFO] [CORE] Core initialization completed
[2025-09-08 08:39:11] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:39:11] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:39:11] [INFO] [TIMEZONE_HANDLER] Converting time
[2025-09-08 08:39:11] [INFO] [TIMEZONE_HANDLER] Time conversion successful
[2025-09-08 08:39:11] [INFO] [TIMEZONE_HANDLER] Session datetime converted
[2025-09-08 08:39:34] [INFO] [API] API handlers initialized
[2025-09-08 08:39:34] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:39:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:34] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:34] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:39:34] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:39:34] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:39:34] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:39:34] [INFO] [CORE] Starting core initialization
[2025-09-08 08:39:34] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:39:34] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:39:34] [INFO] [CORE] Core initialization completed
[2025-09-08 08:39:35] [INFO] [API] API handlers initialized
[2025-09-08 08:39:35] [INFO] [TIMEZONE_HANDLER] Timezone handler initialized
[2025-09-08 08:39:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:39:36] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:39:36] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:39:36] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:39:36] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:39:36] [INFO] [CORE] Starting core initialization
[2025-09-08 08:39:36] [INFO] [CORE] Core initialization completed
[2025-09-08 08:39:36] [INFO] [ADMIN] Settings initialized
[2025-09-08 08:41:35] [INFO] [API] API handlers initialized
[2025-09-08 08:41:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:41:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:41:36] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:41:36] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:41:36] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:41:36] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:41:36] [INFO] [CORE] Starting core initialization
[2025-09-08 08:41:36] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 08:41:36] [INFO] [CORE] Admin functionality loaded
[2025-09-08 08:41:36] [INFO] [CORE] Core initialization completed
[2025-09-08 08:41:36] [INFO] [API] API handlers initialized
[2025-09-08 08:41:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:41:37] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 08:41:37] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 08:41:37] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 08:41:37] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 08:41:37] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 08:41:37] [INFO] [CORE] Starting core initialization
[2025-09-08 08:41:37] [INFO] [CORE] Core initialization completed
[2025-09-08 08:41:37] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:40:52] [INFO] [API] API handlers initialized
[2025-09-08 09:40:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:40:53] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:40:53] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:40:53] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:40:53] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:40:53] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:40:53] [INFO] [CORE] Starting core initialization
[2025-09-08 09:40:53] [INFO] [CORE] Core initialization completed
[2025-09-08 09:40:55] [INFO] [API] API handlers initialized
[2025-09-08 09:40:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:40:56] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:40:56] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:40:56] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:40:56] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:40:56] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:40:56] [INFO] [CORE] Starting core initialization
[2025-09-08 09:40:56] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:16] [INFO] [API] API handlers initialized
[2025-09-08 09:41:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:17] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:17] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:17] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:17] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:17] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:17] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:17] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:27] [INFO] [API] API handlers initialized
[2025-09-08 09:41:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:33] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:33] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:33] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:33] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:33] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:33] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:33] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:35] [INFO] [API] API handlers initialized
[2025-09-08 09:41:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:36] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:36] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:36] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:36] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:36] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:36] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:36] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:46] [INFO] [API] API handlers initialized
[2025-09-08 09:41:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:46] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:46] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:46] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:46] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:46] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:46] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:46] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:48] [INFO] [API] API handlers initialized
[2025-09-08 09:41:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:48] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:48] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:48] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:48] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:48] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:48] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:48] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:41:48] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:41:48] [INFO] [CORE] Core initialization completed
[2025-09-08 09:41:49] [INFO] [ADMIN] Admin menu created
[2025-09-08 09:41:50] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:41:58] [INFO] [API] API handlers initialized
[2025-09-08 09:41:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:41:59] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:41:59] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:41:59] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:41:59] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:41:59] [INFO] [CORE] Starting core initialization
[2025-09-08 09:41:59] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:41:59] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:41:59] [INFO] [CORE] Core initialization completed
[2025-09-08 09:42:01] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:42:58] [INFO] [API] API handlers initialized
[2025-09-08 09:42:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:42:58] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:42:58] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:42:58] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:42:58] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:42:58] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:42:58] [INFO] [CORE] Starting core initialization
[2025-09-08 09:42:58] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:42:58] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:42:58] [INFO] [CORE] Core initialization completed
[2025-09-08 09:42:59] [INFO] [API] API handlers initialized
[2025-09-08 09:42:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:42:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:42:59] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:42:59] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:42:59] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:42:59] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:42:59] [INFO] [CORE] Starting core initialization
[2025-09-08 09:42:59] [INFO] [CORE] Core initialization completed
[2025-09-08 09:43:00] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:44:59] [INFO] [API] API handlers initialized
[2025-09-08 09:44:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:44:59] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:44:59] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:44:59] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:44:59] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:44:59] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:44:59] [INFO] [CORE] Starting core initialization
[2025-09-08 09:44:59] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:44:59] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:44:59] [INFO] [CORE] Core initialization completed
[2025-09-08 09:45:00] [INFO] [API] API handlers initialized
[2025-09-08 09:45:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:45:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:45:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:45:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:45:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:45:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:45:01] [INFO] [CORE] Starting core initialization
[2025-09-08 09:45:01] [INFO] [CORE] Core initialization completed
[2025-09-08 09:45:01] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:47:00] [INFO] [API] API handlers initialized
[2025-09-08 09:47:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:47:00] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:47:00] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:47:00] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:47:00] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:47:00] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:47:00] [INFO] [CORE] Starting core initialization
[2025-09-08 09:47:00] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:47:00] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:47:00] [INFO] [CORE] Core initialization completed
[2025-09-08 09:47:01] [INFO] [API] API handlers initialized
[2025-09-08 09:47:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:47:02] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:47:02] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:47:02] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:47:02] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:47:02] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:47:02] [INFO] [CORE] Starting core initialization
[2025-09-08 09:47:02] [INFO] [CORE] Core initialization completed
[2025-09-08 09:47:02] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:49:01] [INFO] [API] API handlers initialized
[2025-09-08 09:49:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:49:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:49:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:49:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:49:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:49:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:49:01] [INFO] [CORE] Starting core initialization
[2025-09-08 09:49:01] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:49:01] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:49:01] [INFO] [CORE] Core initialization completed
[2025-09-08 09:49:02] [INFO] [API] API handlers initialized
[2025-09-08 09:49:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:49:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:49:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:49:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:49:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:49:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:49:03] [INFO] [CORE] Starting core initialization
[2025-09-08 09:49:03] [INFO] [CORE] Core initialization completed
[2025-09-08 09:49:03] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:51:02] [INFO] [API] API handlers initialized
[2025-09-08 09:51:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:51:03] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:51:03] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:51:03] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:51:03] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:51:03] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:51:03] [INFO] [CORE] Starting core initialization
[2025-09-08 09:51:03] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 09:51:03] [INFO] [CORE] Admin functionality loaded
[2025-09-08 09:51:03] [INFO] [CORE] Core initialization completed
[2025-09-08 09:51:06] [INFO] [API] API handlers initialized
[2025-09-08 09:51:07] [INFO] [ADMIN] Settings initialized
[2025-09-08 09:51:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:51:07] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 09:51:07] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 09:51:07] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 09:51:07] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 09:51:07] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 09:51:07] [INFO] [CORE] Starting core initialization
[2025-09-08 09:51:07] [INFO] [CORE] Core initialization completed
[2025-09-08 11:58:22] [INFO] [API] API handlers initialized
[2025-09-08 11:59:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 11:59:01] [INFO] [WOOCOMMERCE] WooCommerce integration initialized with essential hooks: processing + payment_complete
[2025-09-08 11:59:01] [INFO] [INTEGRATION] WooCommerce integration loaded and initialized
[2025-09-08 11:59:01] [INFO] [MASTERSTUDY] MasterStudy LMS integration initialized with automation
[2025-09-08 11:59:01] [INFO] [INTEGRATION] MasterStudy LMS integration loaded and initialized
[2025-09-08 11:59:01] [INFO] [CORE] VedMG ClassRoom plugin initialized
[2025-09-08 11:59:01] [INFO] [CORE] Starting core initialization
[2025-09-08 11:59:01] [INFO] [ADMIN] Admin interface initialized
[2025-09-08 11:59:01] [INFO] [CORE] Admin functionality loaded
[2025-09-08 11:59:01] [INFO] [CORE] Core initialization completed
