<?php
/**
 * VedMG ClassRoom Admin Dashboard
 * 
 * This is the main dashboard page for the VedMG ClassRoom plugin.
 * It displays course information, student enrollments, and management options.
 * Shows both placeholder data and real database data.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed dashboard page');

// Get real data from database
$real_courses = VedMG_ClassRoom_Database_Helper::get_courses();
$real_enrollments = VedMG_ClassRoom_Database_Helper::get_student_enrollments();
$real_sessions = VedMG_ClassRoom_Database_Helper::get_class_sessions();
$dashboard_stats = VedMG_ClassRoom_Database_Helper::get_dashboard_stats();
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>VedMG ClassRoom Dashboard</h1>
        <p>Manage your courses, students, and Google Classroom integrations</p>
    </div>
    
    <!-- Course Management Section -->
    <div class="vedmg-classroom-section">
        <h2>Course Management</h2>
        <p>Here you can view all courses created by instructors and manage Google Classroom integration.</p>
        
        <!-- Test Button for Development -->
        <button class="vedmg-classroom-btn vedmg-test-btn">
            <span class="vedmg-classroom-spinner"></span>
            Test AJAX Connection
        </button>
        
        <!-- Course Management Table (Real Data + Placeholders) -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th>Course Name</th>
                    <th>Instructor</th>
                    <th>Created Date</th>
                    <th>Students Enrolled</th>
                    <th>Classroom Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($real_courses)): ?>
                    <?php foreach ($real_courses as $course): ?>
                        <tr class="real-data-row">
                            <td>
                                <strong><?php echo esc_html($course->course_name); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($course->instructor_name ?: 'Unknown'); ?></td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_date($course->created_date); ?></td>
                            <td><?php echo intval($course->student_count); ?></td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_classroom_status($course->classroom_status); ?></td>
                            <td>
                                <?php if ($course->classroom_status === 'pending'): ?>
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" data-course-id="<?php echo $course->course_id; ?>">Create Classroom</button>
                                <?php else: ?>
                                    <button class="vedmg-classroom-btn" data-course-id="<?php echo $course->course_id; ?>">Manage</button>
                                <?php endif; ?>
                                <button class="vedmg-classroom-btn vedmg-classroom-btn-danger" data-course-id="<?php echo $course->course_id; ?>">Delete</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Placeholder Data (will be removed later) -->
                <tr class="placeholder-data-row" style="opacity: 0.7;">
                    <td>
                        Sample Course 1
                        <small style="display: block; color: #999;">(Placeholder)</small>
                    </td>
                    <td>John Doe</td>
                    <td>2025-01-01</td>
                    <td>15</td>
                    <td><span class="vedmg-status-active">Active</span></td>
                    <td>
                        <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Create Classroom</button>
                        <button class="vedmg-classroom-btn vedmg-classroom-btn-danger">Delete</button>
                    </td>
                </tr>
                <tr class="placeholder-data-row" style="opacity: 0.7;">
                    <td>
                        Sample Course 2
                        <small style="display: block; color: #999;">(Placeholder)</small>
                    </td>
                    <td>Jane Smith</td>
                    <td>2025-01-02</td>
                    <td>8</td>
                    <td><span class="vedmg-status-pending">Pending</span></td>
                    <td>
                        <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Create Classroom</button>
                        <button class="vedmg-classroom-btn vedmg-classroom-btn-danger">Delete</button>
                    </td>
                </tr>
                
                <?php if (empty($real_courses)): ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                            <em>No courses found in database. Activate the plugin to create database tables, then add some courses.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Student Enrollment Section -->
    <div class="vedmg-classroom-section">
        <h2>Student Enrollments</h2>
        <p>Manage student enrollments and assign them to Google Classroom classes.</p>
        
        <!-- Student Enrollment Table (Real Data + Placeholders) -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Course</th>
                    <th>Google Classroom</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($real_enrollments)): ?>
                    <?php foreach ($real_enrollments as $enrollment): ?>
                        <tr class="real-data-row">
                            <td>
                                <strong><?php echo esc_html($enrollment->student_name ?: 'Unknown Student'); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($enrollment->student_email ?: 'No email'); ?></td>
                            <td><?php echo esc_html($enrollment->student_phone ?: 'No phone'); ?></td>
                            <td><?php echo esc_html($enrollment->course_name ?: 'Unknown Course'); ?></td>
                            <td>
                                <select class="classroom-select" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>">
                                    <option value="">Select Classroom</option>
                                    <?php foreach ($classroom_options as $classroom): ?>
                                        <option value="<?php echo $classroom->course_id; ?>" 
                                                <?php selected($enrollment->google_classroom_id, $classroom->google_classroom_id); ?>>
                                            <?php echo esc_html($classroom->course_name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_enrollment_status($enrollment->enrollment_status); ?></td>
                            <td>
                                <button class="vedmg-classroom-btn vedmg-enroll-btn" data-student-id="<?php echo $enrollment->student_id; ?>" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>">
                                    <?php echo ($enrollment->enrollment_status === 'enrolled') ? 'Update' : 'Enroll'; ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Placeholder Data (will be removed later) -->
                <tr class="placeholder-data-row" style="opacity: 0.7;">
                    <td>
                        Alice Johnson
                        <small style="display: block; color: #999;">(Placeholder)</small>
                    </td>
                    <td><EMAIL></td>
                    <td>+1-555-0123</td>
                    <td>Sample Course 1</td>
                    <td>
                        <select class="classroom-select">
                            <option value="">Select Classroom</option>
                            <option value="1">Math Class A</option>
                            <option value="2">Math Class B</option>
                        </select>
                    </td>
                    <td><span class="vedmg-status-pending">Pending</span></td>
                    <td>
                        <button class="vedmg-classroom-btn vedmg-enroll-btn" data-student-id="1">Enroll</button>
                    </td>
                </tr>
                <tr class="placeholder-data-row" style="opacity: 0.7;">
                    <td>
                        Bob Wilson
                        <small style="display: block; color: #999;">(Placeholder)</small>
                    </td>
                    <td><EMAIL></td>
                    <td>+1-555-0456</td>
                    <td>Sample Course 2</td>
                    <td>
                        <select class="classroom-select">
                            <option value="">Select Classroom</option>
                            <option value="3">Science Class A</option>
                            <option value="4">Science Class B</option>
                        </select>
                    </td>
                    <td><span class="vedmg-status-active">Enrolled</span></td>
                    <td>
                        <button class="vedmg-classroom-btn vedmg-enroll-btn" data-student-id="2">Enroll</button>
                    </td>
                </tr>
                
                <?php if (empty($real_enrollments)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                            <em>No student enrollments found in database. Students will appear here after purchasing courses.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Class Sessions Section -->
    <div class="vedmg-classroom-section">
        <h2>Class Sessions</h2>
        <p>View and manage scheduled Google Meet sessions for your courses.</p>
        
        <!-- Class Sessions Table (Real Data + Placeholders) -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th>Session Title</th>
                    <th>Course</th>
                    <th>Instructor</th>
                    <th>Scheduled Date</th>
                    <th>Time</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($real_sessions)): ?>
                    <?php foreach ($real_sessions as $session): ?>
                        <tr class="real-data-row">
                            <td>
                                <strong><?php echo esc_html($session->session_title); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($session->course_name ?: 'Unknown Course'); ?></td>
                            <td><?php echo esc_html($session->instructor_name ?: 'Unknown'); ?></td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_date($session->scheduled_date); ?></td>
                            <td>
                                <?php echo VedMG_ClassRoom_Database_Helper::format_time($session->start_time); ?> - 
                                <?php echo VedMG_ClassRoom_Database_Helper::format_time($session->end_time); ?>
                            </td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_enrollment_status($session->session_status); ?></td>
                            <td>
                                <?php if (!empty($session->google_meet_link)): ?>
                                    <a href="<?php echo esc_url($session->google_meet_link); ?>" class="vedmg-classroom-btn" target="_blank">Join Meet</a>
                                <?php endif; ?>
                                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" data-session-id="<?php echo $session->session_id; ?>">Edit</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Placeholder Data (will be removed later) -->
                <tr class="placeholder-data-row" style="opacity: 0.7;">
                    <td>
                        Introduction to Mathematics
                        <small style="display: block; color: #999;">(Placeholder)</small>
                    </td>
                    <td>Sample Course 1</td>
                    <td>John Doe</td>
                    <td>Jul 15, 2025</td>
                    <td>10:00 AM - 11:00 AM</td>
                    <td><span class="vedmg-status-pending">Scheduled</span></td>
                    <td>
                        <a href="#" class="vedmg-classroom-btn">Join Meet</a>
                        <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Edit</button>
                    </td>
                </tr>
                
                <?php if (empty($real_sessions)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                            <em>No class sessions scheduled yet. Create courses and schedule sessions to see them here.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Quick Stats Section (Real Data + Placeholders) -->
    <div class="vedmg-classroom-section">
        <h2>Quick Statistics</h2>
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 200px;">
                <h3>Total Courses</h3>
                <p style="font-size: 24px; font-weight: bold; color: #0073aa;">
                    <?php echo $dashboard_stats['courses']; ?>
                    <?php if ($dashboard_stats['courses'] > 0): ?>
                        <small style="display: block; font-size: 12px; color: #666;">(Real Data)</small>
                    <?php else: ?>
                        <small style="display: block; font-size: 12px; color: #999;">(No real data yet)</small>
                    <?php endif; ?>
                </p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h3>Total Students</h3>
                <p style="font-size: 24px; font-weight: bold; color: #46b450;">
                    <?php echo $dashboard_stats['enrollments']; ?>
                    <?php if ($dashboard_stats['enrollments'] > 0): ?>
                        <small style="display: block; font-size: 12px; color: #666;">(Real Data)</small>
                    <?php else: ?>
                        <small style="display: block; font-size: 12px; color: #999;">(No real data yet)</small>
                    <?php endif; ?>
                </p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h3>Active Classrooms</h3>
                <p style="font-size: 24px; font-weight: bold; color: #ffb900;">
                    <?php echo $dashboard_stats['active_classrooms']; ?>
                    <?php if ($dashboard_stats['active_classrooms'] > 0): ?>
                        <small style="display: block; font-size: 12px; color: #666;">(Real Data)</small>
                    <?php else: ?>
                        <small style="display: block; font-size: 12px; color: #999;">(No real data yet)</small>
                    <?php endif; ?>
                </p>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <h3>Pending Enrollments</h3>
                <p style="font-size: 24px; font-weight: bold; color: #dc3232;">
                    <?php echo $dashboard_stats['pending_enrollments']; ?>
                    <?php if ($dashboard_stats['pending_enrollments'] > 0): ?>
                        <small style="display: block; font-size: 12px; color: #666;">(Real Data)</small>
                    <?php else: ?>
                        <small style="display: block; font-size: 12px; color: #999;">(No real data yet)</small>
                    <?php endif; ?>
                </p>
            </div>
        </div>
        
        <!-- Database Status Info -->
        <div style="margin-top: 20px; padding: 15px; background: #f0f0f1; border-radius: 4px;">
            <h4 style="margin: 0 0 10px 0;">Database Status</h4>
            <p style="margin: 0; color: #666;">
                <?php if ($dashboard_stats['courses'] > 0 || $dashboard_stats['enrollments'] > 0): ?>
                    ✅ Database tables are active and containing data. Real data is being displayed above.
                <?php else: ?>
                    ℹ️ Database tables exist but contain no data yet. Add courses and enrollments to see real data.
                <?php endif; ?>
            </p>
        </div>
    </div>
</div>

<script>
// Additional JavaScript for this page can be added here
jQuery(document).ready(function($) {
    console.log('Dashboard page loaded');
    
    // Any page-specific JavaScript
});
</script>
