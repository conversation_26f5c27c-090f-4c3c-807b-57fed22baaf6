# VedMG ClassRoom Database Documentation

This document explains every database table the plugin uses and what each file in the `database/` folder does. It also maps schema changes across upgrade versions. Operational scripts (like `force_db_upgrade.php`) are intentionally excluded from this document.

## Overview
- Activation creates 4 essential tables; upgrades add extra columns/tables as the plugin evolves
- Current database version (upgrade target): 2.1
- Automatic upgrade runs on admin pages via `admin_init`; repairs/force upgrade are available

## Tables

### 1) wp_vedmg_courses — Course catalog and Google Classroom linkage
Key columns:
- course_id (PK, bigint) — Internal course ID
- masterstudy_course_id (bigint, unique) — Maps to MasterStudy LMS course
- course_name (varchar), course_description (text)
- instructor_id (bigint, WP user ID)
- instructor_name (varchar, v1.1) — Denormalized display name (populated by upgrades)
- instructor_email (varchar)
- class_join_link (text, v1.1) — Join link
- google_classroom_id (varchar, unique), google_classroom_link (text)
- calendar_id (varchar) — Calendar used for scheduling
- meeting_link (text, v1.6 or present from activator) — Direct meeting link for the course
- classroom_status (enum: pending, created, active, archived)
- course_status (enum: draft, published, archived)
- auto_enroll_enabled (tinyint) — Auto‑enroll students on purchase
- created_date, updated_date (datetime)
- masterstudy_sync_status (enum) — Sync state with MasterStudy
- woocommerce_product_id (int) — Product mapping

Notes:
- v1.1 upgrade adds instructor_name and class_join_link if missing
- v1.6 upgrade ensures meeting_link exists (activator also creates it)

### 2) wp_vedmg_student_enrollments — Student purchases and enrollment details
Base columns (v1.0):
- enrollment_id (PK), student_id, student_name, student_email, student_phone
- course_id, woocommerce_order_id, google_classroom_id
- purchase_date, enrollment_date, completion_date
- enrollment_status (enum) — Removed in v2.0
- created_date, updated_date

Changes in v2.0:
- Remove column: enrollment_status
- Add: instructor_name (varchar)
- Add scheduling tracking fields:
  - last_scheduled_date (datetime)
  - total_sessions_scheduled (int)
  - last_session_type (enum: individual, group, class_wide)
- Backup table created during migration: `wp_vedmg_student_enrollments_backup_v1`

### 3) wp_vedmg_class_sessions — Scheduled/held sessions per course
Base columns (v1.0 from activator):
- session_id (PK), course_id, google_classroom_id
- session_title, session_description, google_meet_link
- scheduled_date (date), start_time (time), end_time (time)
- session_status (enum: scheduled, ongoing, completed, cancelled)
- attendance_required (tinyint), max_participants (int)
- created_date, updated_date

Upgrades:
- v1.2: assigned_instructor_id (bigint, indexed)
- v1.3: is_recurring (boolean), recurrence_pattern (JSON), meeting_type (enum: individual, class), target_student_id (int)
- v1.4: session_type (enum: individual, group, class), duration_minutes (int),
        is_recurring (tinyint), recurring_pattern (varchar), recurring_days (JSON),
        recurring_dates (JSON), recurring_count (int), recurring_end_date (date),
        selected_student_ids (JSON)
- v1.5: session_status enum updated to include 'google classroom'
- v2.1: featured flags for admin page support
  - is_featured (tinyint), featured_date (datetime)

### 4) wp_vedmg_instructor_sync — Instructor integration state
- instructor_sync_id (PK), wordpress_user_id (unique)
- instructor_name, instructor_email, instructor_phone
- sync_status (enum), google_sync_status (enum)
- last_synced_date, last_google_sync_date
- google_classroom_count (int), sync_error_message (text)
- created_date, updated_date

### 5) wp_vedmg_student_classroom_mappings (v1.3) — Google Classroom membership mapping
- id (PK), student_id, student_email, google_classroom_id, course_id
- enrollment_status (enum: enrolled, pending, removed)
- fetched_at, updated_at; indexes for lookups

### 6) wp_vedmg_session_tracking (v2.0) — Fine‑grained session tracking
- tracking_id (PK), enrollment_id, student_id, course_id, session_id (nullable)
- session_type (enum: individual, group, class_wide)
- session_status (enum: scheduled, completed, cancelled, rescheduled)
- scheduled_date, completed_date, session_details (text), google_event_id (varchar)
- created_date, updated_date; useful indexes for status/date scans

## Files in database/ - Detailed Functionality

### activator.php - Database Creation & Initial Setup
**Primary Purpose**: Creates all essential database tables when the plugin is first activated.

**What it does to the database**:
- Creates 4 core tables with complete schema:
  - `wp_vedmg_courses` - Course catalog with Google Classroom integration fields
  - `wp_vedmg_student_enrollments` - Student purchase and enrollment tracking
  - `wp_vedmg_class_sessions` - Session scheduling and management
  - `wp_vedmg_instructor_sync` - Instructor synchronization state tracking
- Sets initial database version to 1.0 in WordPress options table
- Creates all necessary indexes and foreign key relationships
- Establishes proper charset and collation for all tables
- Logs all creation activities via the debug system

**Key Functions**:
- `activate()` - Main activation orchestrator
- `create_courses_table()` - Creates course management table
- `create_student_enrollments_table()` - Creates enrollment tracking table
- `create_class_sessions_table()` - Creates session scheduling table
- `create_instructor_sync_table()` - Creates instructor sync table
- `verify_tables_exist()` - Validates all tables were created successfully
- `get_database_stats()` - Returns table creation statistics

**When it runs**: Only during plugin activation via WordPress activation hook.

### upgrade.php - Database Schema Evolution & Migration
**Primary Purpose**: Manages database schema changes across plugin versions and handles migrations.

**What it does to the database**:
- Compares current DB version with target version (2.1)
- Executes incremental schema upgrades in proper sequence
- Adds new columns to existing tables without data loss
- Creates new tables for enhanced functionality
- Modifies existing column definitions (like ENUM values)
- Backs up critical data before major changes
- Updates database version tracking in WordPress options

**Version-specific database changes**:
- v1.1: Adds instructor_name, class_join_link to courses table + populates existing data
- v1.2: Adds assigned_instructor_id column to sessions table + creates index
- v1.3: Creates student_classroom_mappings table + adds recurring/meeting columns to sessions
- v1.4: Adds comprehensive scheduling columns to sessions table (recurring patterns, student selection)
- v1.5: Modifies session_status ENUM to include 'google classroom' option
- v1.6: Adds meeting_link column to courses table
- v2.0: Major enrollment table restructuring - removes status column, adds instructor tracking, creates session_tracking table
- v2.1: Adds featured flags (is_featured, featured_date) to sessions table

**Key Functions**:
- `check_and_upgrade()` - Automatic upgrade checker (runs on admin_init)
- `perform_upgrades()` - Orchestrates sequential version upgrades
- `upgrade_to_X_X()` - Individual version upgrade methods
- `force_upgrade()` - Emergency function to run all upgrades regardless of version
- `emergency_repair()` - Recreates missing tables + forces upgrades

**When it runs**: Automatically on admin page loads, or manually via force functions.

### helper.php - Database Query Interface & Runtime Repairs
**Primary Purpose**: Provides safe, optimized database queries for admin interface and handles runtime column repairs.

**What it does to the database**:
- Executes READ operations for admin pages (courses, enrollments, sessions, instructors)
- Implements pagination, filtering, and search across all tables
- Performs runtime column verification and repair for sessions table
- Handles complex JOIN queries between related tables
- Manages instructor session reassignment operations
- Provides data formatting and status mapping functions
- Implements one-time column checks to avoid performance overhead

**Database operations**:
- `get_courses()` - Paginated course listing with instructor details
- `get_student_enrollments()` - Enrollment data with course/student information
- `get_class_sessions()` - Session listing with course and instructor data
- `get_instructors()` - Instructor management with statistics
- `ensure_sessions_table_columns()` - Runtime repair for missing columns
- `reassign_instructor_sessions()` - Bulk session reassignment operations

**Critical repair functionality**:
- Checks for missing columns in sessions table (is_featured, featured_date, etc.)
- Adds missing columns with proper definitions if not found
- Ensures ENUM values are up-to-date (session_status includes 'google classroom')
- Runs only once per 24 hours to avoid performance impact
- Provides safety net for sites with incomplete upgrades

**When it runs**: Every time admin pages load data, with smart caching for repairs.

### deactivator.php - Database Cleanup & Data Removal
**Primary Purpose**: Safely removes all plugin data when deactivation is requested.

**What it does to the database**:
- Creates backup of all plugin data before deletion
- Drops all plugin tables in proper dependency order
- Removes all plugin-related WordPress options
- Clears cached data and transients
- Removes any custom cache directories
- Provides verification of complete data removal

**Database cleanup operations**:
- Drops tables: student_classroom_mappings, class_sessions, student_enrollments, instructor_sync, courses
- Removes options: vedmg_classroom_* prefixed options
- Clears transients: _transient_vedmg_classroom_* entries
- Backup creation: Creates SQL dump before deletion
- Cache clearing: Flushes WordPress object cache

**Safety features**:
- Optional data preservation mode (deactivate without deleting)
- Automatic backup creation with timestamp
- Transaction-based operations for data integrity
- Verification functions to confirm complete removal
- Detailed logging of all cleanup operations

**Key Functions**:
- `deactivate()` - Main deactivation orchestrator with optional data preservation
- `drop_all_tables()` - Removes all plugin tables in dependency order
- `backup_data_before_deletion()` - Creates SQL backup of all data
- `remove_plugin_options()` - Cleans WordPress options table
- `verify_complete_removal()` - Confirms all data was removed

**When it runs**: Only when explicitly called during plugin deactivation (not automatic).

### README.md - Documentation
**Primary Purpose**: Comprehensive documentation of database structure and file functionality.

**What it contains**:
- Complete table schemas with field descriptions
- Version history and migration details
- File functionality explanations
- Usage guidelines and recommendations

- README.md (this file)

Note: Operational scripts are excluded from this documentation.

## Version timeline (what changes where)
- v1.1 — courses: instructor_name, class_join_link; backfill names
- v1.2 — class_sessions: assigned_instructor_id (+index)
- v1.3 — add student_classroom_mappings table; sessions: recurring & meeting_type/target_student_id
- v1.4 — sessions: session_type, duration, recurring_* + selected_student_ids
- v1.5 — sessions: session_status enum gains 'google classroom'
- v1.6 — courses: meeting_link (also present from activator)
- v2.0 — enrollments: remove enrollment_status, add instructor/scheduling, add session_tracking table, backup enrollments
- v2.1 — sessions: add is_featured, featured_date

## Database File Interaction Summary

**File execution order during plugin lifecycle**:
1. **activator.php** - Runs once on plugin activation to create initial database structure
2. **upgrade.php** - Runs automatically on admin pages to evolve database schema over time
3. **helper.php** - Runs continuously during admin usage to query data and repair missing columns
4. **deactivator.php** - Runs only when explicitly called to remove all plugin data

**Data flow between files**:
- activator.php → Creates base tables → Sets version 1.0
- upgrade.php → Detects version 1.0 → Runs migrations → Updates to version 2.1
- helper.php → Queries evolved tables → Repairs any missing columns → Serves admin interface
- deactivator.php → Backs up all data → Removes everything → Cleans WordPress options

## Database Safety & Recovery Features
- **Automatic backups**: deactivator.php creates SQL dumps before deletion
- **Runtime repairs**: helper.php fixes missing columns without manual intervention
- **Transaction safety**: Major upgrades use database transactions for rollback capability
- **Version tracking**: WordPress options store current database version for upgrade decisions
- **Emergency functions**: force_upgrade() and emergency_repair() for broken installations
- **Dependency management**: Tables dropped in proper order to avoid foreign key conflicts

## Performance Considerations
- **One-time checks**: helper.php caches column verification for 24 hours to avoid overhead
- **Indexed columns**: All foreign keys and frequently queried columns have proper indexes
- **Pagination**: All admin queries implement LIMIT/OFFSET for large datasets
- **Prepared statements**: All queries use WordPress $wpdb->prepare() for security and performance

## Notes and Recommendations
- **Upgrades run only on admin requests**; for headless/CLI environments, use force upgrade or emergency repair
- **For sites that missed older migrations**, the helper’s one‑time column check provides additional safety, but the 2.1 migration now covers featured fields explicitly
- **Always test deactivator.php in staging** before using in production as it permanently deletes all data
- **Monitor debug logs** during upgrades to catch any schema modification issues early
