/**
 * VedMG ClassRoom Enrollments JavaScript
 * 
 * JavaScript functionality specific to the student enrollments page.
 * Handles enrollment actions, filtering, and student management.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

(function($) {
    'use strict';

    /**
     * Enrollments management specific functionality
     */
    var VedMGEnrollments = {
        
        /**
         * Initialize enrollment management functionality
         */
        init: function() {
            console.log('VedMG ClassRoom Enrollments initialized');
            
            // Bind enrollment specific events
            this.bindEvents();
            
            // Initialize enrollment components
            this.initComponents();
            
            // Update enrollment counts
            this.updateEnrollmentCounts();
        },
        
        /**
         * Bind enrollment specific events
         */
        bindEvents: function() {
            // Unbind existing events first to prevent duplicates
            $(document).off('click', '.vedmg-enroll-btn');
            $(document).off('click', '.vedmg-view-student-btn');
            $(document).off('click', '.vedmg-edit-enrollment-btn');
            $(document).off('click', '.vedmg-schedule-lab-btn');
            $(document).off('click', '.vedmg-modal-close, #cancel-enrollment-edit, #cancel-schedule-lab');
            $(document).off('submit', '#vedmg-enrollment-edit-form');
            $(document).off('submit', '#vedmg-schedule-lab-form');
            $(document).off('click', '#apply-bulk-action');
            $(document).off('change', '#select-all-enrollments');
            $(document).off('change', '.enrollment-checkbox');
            $(document).off('click', '#refresh-enrollments');
            $(document).off('click', '#sync-woocommerce');
            $(document).off('click', '#fetch-classroom-data');
            $(document).off('change', '#lab-recurring');
            
            // Bind enrollment actions
            $(document).on('click', '.vedmg-enroll-btn', this.handleEnrollStudent);
            $(document).on('click', '.vedmg-view-student-btn', this.handleViewStudent);
            $(document).on('click', '.vedmg-edit-enrollment-btn', this.handleEditEnrollment);
            $(document).on('click', '.vedmg-schedule-lab-btn', this.handleScheduleLab);
            
            // Modal events
            $(document).on('click', '.vedmg-modal-close, #cancel-enrollment-edit, #cancel-schedule-lab', this.closeModals);
            $(document).on('click', '.vedmg-modal', function(e) {
                if (e.target === this) {
                    VedMGEnrollments.closeModals();
                }
            });
            $(document).on('submit', '#vedmg-enrollment-edit-form', this.handleSaveEnrollment);
            // Schedule lab form submission is handled directly in enrollments.php to avoid conflicts
            
            // Bulk actions
            $(document).on('click', '#apply-bulk-action', this.handleBulkAction);
            $(document).on('change', '#select-all-enrollments', this.handleSelectAll);
            $(document).on('change', '.enrollment-checkbox', this.handleEnrollmentSelect);
            
            // Refresh enrollments
            $(document).on('click', '#refresh-enrollments', this.handleRefreshEnrollments);
            
            // Sync actions
            $(document).on('click', '#sync-woocommerce', this.handleSyncWooCommerce);
            $(document).on('click', '#fetch-classroom-data', this.handleFetchClassroomData);
            
            // Schedule Lab modal interactions
            $(document).on('change', '#lab-recurring', this.toggleRecurringOptions);
            
            console.log('Enrollment events bound (server-side pagination enabled)');
        },
        
        /**
         * Initialize enrollment components
         */
        initComponents: function() {
            // Initialize enrollment table
            this.initEnrollmentTable();
            
            console.log('Enrollment components initialized (server-side pagination)');
        },
        
        /**
         * Initialize enrollment table
         */
        initEnrollmentTable: function() {
            // Add row interactions for enrollment rows
            $('.vedmg-classroom-table tbody tr.enrollment-row').each(function() {
                var $row = $(this);
                var enrollmentId = $row.data('enrollment-id');
                var studentId = $row.data('student-id');
                
                if (enrollmentId) {
                    $row.attr('data-enrollment-id', enrollmentId);
                }
                if (studentId) {
                    $row.attr('data-student-id', studentId);
                }
            });
            
            console.log('Enrollment table initialized with server-side data');
        },
        
        /**
         * Initialize filters
         */
        initFilters: function() {
            // Set up filter change handlers
            $('#course-filter, #status-filter').on('change', function() {
                VedMGEnrollments.applyFilters();
            });
        },
        
        /**
         * Handle enroll student action
         */
        handleEnrollStudent: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            
            // Prevent multiple submissions
            if ($button.hasClass('processing')) {
                console.log('Already processing enrollment, ignoring click');
                return;
            }
            
            var studentId = $button.data('student-id');
            var enrollmentId = $button.data('enrollment-id');
            var $row = $button.closest('tr');
            var studentName = $row.find('td:nth-child(2) strong').text();
            var $classroomSelect = $row.find('.classroom-select');
            var classroomId = $classroomSelect.val();
            
            // Validate classroom selection
            if (!classroomId) {
                alert('Please select a Google Classroom first.');
                $classroomSelect.focus();
                return;
            }
            
            var classroomName = $classroomSelect.find('option:selected').text();
            
            // Confirm enrollment
            var action = $button.text().includes('Update') ? 'update' : 'enroll';
            var message = action === 'update' 
                ? 'Update classroom assignment for "' + studentName + '"?'
                : 'Enroll "' + studentName + '" in "' + classroomName + '"?';
            
            if (!confirm(message)) {
                return;
            }
            
            // Mark as processing to prevent duplicate submissions
            $button.addClass('processing');
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text(action === 'update' ? 'Updating...' : 'Enrolling...');
            
            // Perform enrollment
            VedMGEnrollments.enrollStudent(studentId, enrollmentId, classroomId, classroomName, $button, $row);
            
            console.log('Enrolling student:', {
                studentId: studentId,
                enrollmentId: enrollmentId,
                classroomId: classroomId,
                action: action
            });
        },
        
        /**
         * Handle view student details
         */
        handleViewStudent: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var studentId = $button.data('student-id');
            var $row = $button.closest('tr');
            var studentData = VedMGEnrollments.extractStudentData($row);
            
            // Open student details modal
            VedMGEnrollments.openStudentDetailsModal(studentData);
            
            console.log('Viewing student details:', studentId);
        },
        
        /**
         * Handle edit enrollment
         */
        handleEditEnrollment: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var enrollmentId = $button.data('enrollment-id');
            var $row = $button.closest('tr');
            var enrollmentData = VedMGEnrollments.extractEnrollmentData($row);
            
            // Open enrollment edit modal
            VedMGEnrollments.openEnrollmentEditModal(enrollmentData);
            
            console.log('Editing enrollment:', enrollmentId);
        },
        
        /**
         * Extract student data from table row
         */
        extractStudentData: function($row) {
            return {
                id: $row.data('student-id'),
                name: $row.find('td:nth-child(2) strong').text().trim(),
                email: $row.find('td:nth-child(3)').text().trim(),
                phone: $row.find('td:nth-child(4)').text().trim(),
                course: $row.find('td:nth-child(5)').text().trim(),
                enrollmentDate: new Date().toLocaleDateString(), // We'll use current date as placeholder
                status: $row.find('.vedmg-enrollment-status').data('status')
            };
        },
        
        /**
         * Extract enrollment data from table row
         */
        extractEnrollmentData: function($row) {
            return {
                id: $row.data('enrollment-id'),
                studentName: $row.find('td:nth-child(2) strong').text().trim(),
                studentEmail: $row.find('td:nth-child(3)').text().trim(),
                studentPhone: $row.find('td:nth-child(4)').text().trim(),
                course: $row.find('td:nth-child(5)').text().trim(),
                enrollmentDate: new Date().toLocaleDateString(), // Placeholder
                status: $row.find('.vedmg-enrollment-status').data('status'),
                classroomStatus: $row.find('td:nth-child(6) select').val() ? 'Assigned' : 'Not Assigned'
            };
        },
        
        /**
         * Open student details modal
         */
        openStudentDetailsModal: function(studentData) {
            console.log('VedMG Debug: Opening student details modal with data:', studentData);
            
            var $modal = $('#vedmg-student-details-modal');
            
            if ($modal.length === 0) {
                console.error('VedMG Debug: Modal element not found!');
                return;
            }
            
            // Populate modal with student data
            $('#details-student-name').text(studentData.name || 'Unknown Student');
            $('#details-student-email').text(studentData.email || 'No email');
            $('#details-student-status').text(studentData.status || 'Unknown').attr('data-status', studentData.status);
            $('#details-student-id').text(studentData.id || '--');
            $('#details-student-join-date').text(studentData.enrollmentDate || '--');
            
            // Set avatar placeholder with first letter
            var firstLetter = (studentData.name && studentData.name.length > 0) ? studentData.name.charAt(0).toUpperCase() : '?';
            $('.vedmg-avatar-placeholder').text(firstLetter);
            
            // Add additional student details if available
            $('#details-student-enrollments').text('1'); // Current enrollment
            $('#details-student-active-courses').text('1'); // Active course
            $('#details-student-last-activity').text('Today'); // Placeholder
            $('#details-student-completion-rate').text('0%'); // Placeholder
            
            // Load enrollment details
            var enrollmentHtml = '<div class="vedmg-enrollment-item">' +
                '<div>' +
                    '<div class="course-name">' + (studentData.course || 'Unknown Course') + '</div>' +
                    '<div class="enrollment-date">Phone: ' + (studentData.phone || 'No phone') + '</div>' +
                    '<div class="enrollment-date">Enrolled: ' + (studentData.enrollmentDate || 'Unknown') + '</div>' +
                '</div>' +
                '<span class="vedmg-enrollment-status" data-status="' + (studentData.status || 'unknown') + '">' + 
                    (studentData.status || 'Unknown') + 
                '</span>' +
                '</div>';
            
            $('#details-student-enrollment-list').html(enrollmentHtml);
            
            // Set the enrollment ID on the edit button for functionality
            $('.vedmg-edit-enrollment-btn').attr('data-enrollment-id', studentData.id || studentData.enrollmentId);
            
            // Show modal with fade effect - same as courses.js
            $modal.fadeIn(300);
            
            console.log('VedMG Debug: Student details modal should now be visible');
        },
        
        /**
         * Open enrollment edit modal
         */
        openEnrollmentEditModal: function(enrollmentData) {
            var $modal = $('#vedmg-enrollment-edit-modal');
            
            // Populate form with enrollment data
            $('#edit-enrollment-id').val(enrollmentData.id);
            $('#edit-student-name').val(enrollmentData.studentName);
            $('#edit-student-email').val(enrollmentData.studentEmail);
            $('#edit-enrollment-status').val(enrollmentData.status);
            
            // Convert date format if needed
            var enrollmentDate = enrollmentData.enrollmentDate;
            if (enrollmentDate && enrollmentDate !== '--') {
                // Try to parse and format the date
                var date = new Date(enrollmentDate);
                if (!isNaN(date.getTime())) {
                    $('#edit-enrollment-date').val(date.toISOString().split('T')[0]);
                }
            }
            
            // Show modal
            $modal.fadeIn(300);
        },
        
        /**
         * Close modals
         */
        closeModals: function() {
            $('.vedmg-modal').fadeOut(300);
        },
        
        /**
         * Handle save enrollment
         */
        handleSaveEnrollment: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $saveBtn = $form.find('#save-enrollment-edit');
            var $spinner = $saveBtn.find('.vedmg-classroom-spinner');
            
            // Prevent multiple submissions
            if ($saveBtn.hasClass('processing')) {
                console.log('Already processing enrollment save, ignoring submission');
                return;
            }
            
            // Mark as processing
            $saveBtn.addClass('processing');
            
            // Show loading state
            $saveBtn.addClass('loading').prop('disabled', true);
            if ($spinner.length) {
                $spinner.show();
            }
            
            // Collect form data
            var formData = {
                enrollment_id: $('#edit-enrollment-id').val(),
                student_name: $('#edit-student-name').val(),
                student_email: $('#edit-student-email').val(),
                course_id: $('#edit-course-id').val(),
                enrollment_status: $('#edit-enrollment-status').val(),
                enrollment_date: $('#edit-enrollment-date').val()
            };
            
            // Make AJAX call to save enrollment data
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'save_enrollment_data',
                    form_data: formData,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    $saveBtn.removeClass('loading processing').prop('disabled', false);
                    if ($spinner.length) {
                        $spinner.hide();
                    }
                    
                    if (response.success) {
                        VedMGClassRoomAdmin.showMessage('Enrollment updated successfully!', 'success');
                        VedMGEnrollments.closeModals();
                        
                        // Refresh the page data
                        VedMGEnrollments.refreshEnrollmentData();
                    } else {
                        VedMGClassRoomAdmin.showMessage('Failed to update enrollment: ' + response.data, 'error');
                    }
                },
                error: function() {
                    $saveBtn.removeClass('loading processing').prop('disabled', false);
                    if ($spinner.length) {
                        $spinner.hide();
                    }
                    VedMGClassRoomAdmin.showMessage('Failed to update enrollment. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Handle refresh enrollments - reload the current page
         */
        handleRefreshEnrollments: function(e) {
            e.preventDefault();
            console.log('Refreshing enrollments page...');
            window.location.reload();
        },
        
        /**
         * Handle sync with WooCommerce
         */
        handleSyncWooCommerce: function(e) {
            e.preventDefault();
            console.log('=== WooCommerce Sync Button Clicked ===');
            
            var $button = $(this);
            console.log('Button element:', $button);
            
            // Confirm sync
            console.log('Showing confirmation dialog...');
            var confirmed = confirm('Sync with WooCommerce?\n\nThis will fetch the latest order/enrollment data from WooCommerce and update the database.');
            
            if (!confirmed) {
                console.log('User cancelled sync operation');
                return;
            }
            
            console.log('User confirmed sync - proceeding...');
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Syncing...');
            console.log('Button state changed to loading');
            
            // Perform sync
            console.log('Calling syncWithWooCommerce function...');
            VedMGEnrollments.syncWithWooCommerce().then(function(result) {
                console.log('=== Sync Success ===');
                console.log('Sync result:', result);
                
                $button.prop('disabled', false);
                $button.text('Sync with WooCommerce');
                console.log('Button state restored');
                
                var message = 'Sync completed! ' + result.message;
                console.log('Success message:', message);
                VedMGClassRoomAdmin.showMessage(message, 'success');
                
                // Refresh the page to show updated data
                console.log('Refreshing page in 2 seconds...');
                setTimeout(function() {
                    console.log('Reloading page now...');
                    window.location.reload();
                }, 2000);
                
            }).catch(function(error) {
                console.log('=== Sync Failed ===');
                console.error('Sync error details:', error);
                
                $button.prop('disabled', false);
                $button.text('Sync with WooCommerce');
                console.log('Button state restored after error');
                
                var errorMessage = 'Sync failed. Please try again.';
                if (error && error.message) {
                    errorMessage += ' Error: ' + error.message;
                }
                console.log('Error message:', errorMessage);
                VedMGClassRoomAdmin.showMessage(errorMessage, 'error');
            });
        },
        
        /**
         * Handle fetch classroom data
         */
        handleFetchClassroomData: function(e) {
            e.preventDefault();
            console.log('=== Fetch Classroom Data Button Clicked ===');
            
            var $button = $(this);
            console.log('Button element:', $button);
            
            // Confirm fetch
            console.log('Showing confirmation dialog...');
            var confirmed = confirm('Fetch Classroom Data?\n\nThis will retrieve the latest student enrollment data from Google Classroom and update the database.');
            
            if (!confirmed) {
                console.log('User cancelled fetch operation');
                return;
            }
            
            console.log('User confirmed fetch - proceeding...');
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Fetching...');
            console.log('Button state changed to loading');
            
            // Perform fetch
            console.log('Calling fetchClassroomData function...');
            VedMGEnrollments.fetchClassroomData().then(function(result) {
                console.log('=== Fetch Success ===');
                console.log('Fetch result:', result);
                
                $button.prop('disabled', false);
                $button.text('Fetch Classroom Data');
                console.log('Button state restored');
                
                var message = 'Classroom data fetched successfully! ' + result.message;
                console.log('Success message:', message);
                VedMGClassRoomAdmin.showMessage(message, 'success');
                
                // Refresh the page to show updated data
                console.log('Refreshing page in 2 seconds...');
                setTimeout(function() {
                    console.log('Reloading page now...');
                    window.location.reload();
                }, 2000);
                
            }).catch(function(error) {
                console.log('=== Fetch Failed ===');
                console.error('Fetch error details:', error);
                
                $button.prop('disabled', false);
                $button.text('Fetch Classroom Data');
                console.log('Button state restored after error');
                
                var errorMessage = 'Failed to fetch classroom data. Please try again.';
                if (error && error.message) {
                    errorMessage += ' Error: ' + error.message;
                }
                console.log('Error message:', errorMessage);
                VedMGClassRoomAdmin.showMessage(errorMessage, 'error');
            });
        },
        
        /**
         * Handle schedule lab button click
         */
        handleScheduleLab: function(e) {
            e.preventDefault();
            console.log('=== Schedule Lab Button Clicked ===');
            
            var $button = $(this);
            var studentId = $button.data('student-id');
            var enrollmentId = $button.data('enrollment-id');
            var courseId = $button.data('course-id');
            
            console.log('Student ID:', studentId);
            console.log('Enrollment ID:', enrollmentId);
            console.log('Course ID:', courseId);
            
            // Set modal data
            $('#lab-student-id').val(studentId);
            $('#lab-enrollment-id').val(enrollmentId);
            $('#lab-course-id').val(courseId);
            
            // Reset form
            $('#vedmg-schedule-lab-form')[0].reset();
            $('#lab-recurring-options').hide();
            $('#lab-recurring').prop('checked', false);
            
            // Set default date to tomorrow
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            var dateString = tomorrow.toISOString().split('T')[0];
            $('#lab-session-date').val(dateString);
            
            // Show modal
            $('#vedmg-schedule-lab-modal').fadeIn();
            console.log('Schedule Lab modal opened');
        },
        
        // handleSaveScheduleLab function removed - form submission now handled directly in enrollments.php
        
        /**
         * Toggle recurring options visibility
         */
        toggleRecurringOptions: function() {
            var isChecked = $(this).is(':checked');
            console.log('Recurring checkbox changed:', isChecked);
            
            if (isChecked) {
                $('#lab-recurring-options').slideDown();
            } else {
                $('#lab-recurring-options').slideUp();
            }
        },
        
        /**
         * Handle bulk actions on selected enrollments
         */
        handleBulkAction: function(e) {
            e.preventDefault();
            console.log('=== Bulk Action Triggered ===');
            
            var action = $('#bulk-action-selector').val();
            console.log('Selected action:', action);
            
            if (!action || action === '-1') {
                console.log('No action selected');
                VedMGClassRoomAdmin.showMessage('Please select an action first.', 'warning');
                return;
            }
            
            var selectedEnrollments = $('.enrollment-checkbox:checked');
            console.log('Selected enrollments count:', selectedEnrollments.length);
            
            if (selectedEnrollments.length === 0) {
                console.log('No enrollments selected');
                VedMGClassRoomAdmin.showMessage('Please select enrollments to perform bulk action.', 'warning');
                return;
            }
            
            var confirmed = confirm('Apply "' + action + '" to ' + selectedEnrollments.length + ' selected enrollment(s)?');
            if (!confirmed) {
                console.log('User cancelled bulk action');
                return;
            }
            
            console.log('Proceeding with bulk action:', action);
            
            // For now, just show a message (implement actual bulk actions later)
            VedMGClassRoomAdmin.showMessage('Bulk action "' + action + '" applied to ' + selectedEnrollments.length + ' enrollment(s).', 'info');
        },
        
        /**
         * Handle select all enrollments checkbox
         */
        handleSelectAll: function(e) {
            console.log('=== Select All Checkbox Triggered ===');
            
            var isChecked = $(this).is(':checked');
            console.log('Select all checked:', isChecked);
            
            $('.enrollment-checkbox').prop('checked', isChecked);
            
            var checkedCount = $('.enrollment-checkbox:checked').length;
            console.log('Updated checked count:', checkedCount);
            
            // Update bulk action button state
            VedMGEnrollments.updateBulkActionState();
        },
        
        /**
         * Handle individual enrollment checkbox selection
         */
        handleEnrollmentSelect: function(e) {
            console.log('=== Individual Enrollment Checkbox Triggered ===');
            
            var checkedCount = $('.enrollment-checkbox:checked').length;
            var totalCount = $('.enrollment-checkbox').length;
            
            console.log('Checked:', checkedCount, 'of', totalCount);
            
            // Update select all checkbox state
            $('#select-all-enrollments').prop('checked', checkedCount === totalCount);
            
            // Update bulk action button state  
            VedMGEnrollments.updateBulkActionState();
        },
        
        /**
         * Update bulk action button state based on selections
         */
        updateBulkActionState: function() {
            var checkedCount = $('.enrollment-checkbox:checked').length;
            var $bulkButton = $('#apply-bulk-action');
            
            if (checkedCount > 0) {
                $bulkButton.prop('disabled', false);
                $bulkButton.removeClass('disabled');
            } else {
                $bulkButton.prop('disabled', true);
                $bulkButton.addClass('disabled');
            }
            
            console.log('Bulk action button state updated - enabled:', checkedCount > 0);
        },
        
        /**
         * Go to next page
         */
        goToNextPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage + 1);
        },
        
        /**
         * Go to previous page
         */
        goToPreviousPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage - 1);
        },
        
        /**
         * Go to last page
         */
        goToLastPage: function() {
            var pageSize = parseInt($('#enrollment-pagination-size-select').val());
            var totalItems = parseInt($('#enrollment-pagination-total').text());
            var lastPage = Math.ceil(totalItems / pageSize);
            this.goToPage(lastPage);
        },
        
        /**
         * Set page size
         */
        setPageSize: function(pageSize) {
            // Reset to page 1 when changing page size
            this.goToPage(1);
            console.log('Enrollments page size changed to:', pageSize);
        },
        
        /**
         * Handle refresh enrollments
         */
        handleRefreshEnrollments: function(e) {
            e.preventDefault();
            
            console.log('Refreshing enrollments data...');
            VedMGClassRoomAdmin.showMessage('Enrollments data refreshed!', 'info');
            
            // Update enrollment counts
            VedMGEnrollments.updateEnrollmentCounts();
        },

        /**
         * Refresh enrollment data
         */
        refreshEnrollmentData: function() {
            console.log('Refreshing enrollment data...');
            VedMGClassRoomAdmin.showMessage('Enrollment data refreshed!', 'info');
        },
        
        /**
         * Enroll student in classroom
         */
        enrollStudent: function(studentId, enrollmentId, classroomId, classroomName, $button, $row) {
            // In real implementation, this would make an API call
            
            setTimeout(function() {
                // Update enrollment status
                var $statusCell = $row.find('.vedmg-enrollment-status');
                $statusCell.html('<span class="vedmg-status-active">Enrolled</span>');
                
                // Update button text and remove processing class
                $button.text('Update');
                $button.prop('disabled', false);
                $button.removeClass('processing');
                
                // Update counts
                VedMGEnrollments.updateEnrollmentCounts();
                
                // Show success message only once
                var studentName = $row.find('td:nth-child(2) strong').text();
                VedMGClassRoomAdmin.showMessage('Successfully enrolled ' + studentName + ' in ' + classroomName + '!', 'success');
                
            }, 1500);
        },
        
        /**
         * Apply filters to enrollment table
         */
        applyFilters: function() {
            var courseFilter = $('#course-filter').val();
            var statusFilter = $('#status-filter').val();
            
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var courseText = $row.find('td:nth-child(5)').text().trim();
                var status = $row.find('.vedmg-enrollment-status').attr('data-status');
                
                var showRow = true;
                
                // Apply course filter
                if (courseFilter && !courseText.toLowerCase().includes(courseFilter.toLowerCase())) {
                    showRow = false;
                }
                
                // Apply status filter
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // Show/hide row
                if (showRow) {
                    $row.show();
                } else {
                    $row.hide();
                }
            });
            
            // Update counts after filtering
            VedMGEnrollments.updateEnrollmentCounts();
            VedMGEnrollments.updatePagination();
        },
        
        /**
         * Clear all filters
         */
        clearFilters: function() {
            $('#course-filter, #status-filter').val('');
            $('.vedmg-classroom-table tbody tr').show();
            VedMGEnrollments.updateEnrollmentCounts();
            VedMGEnrollments.updatePagination();
        },
        
        /**
         * Perform bulk action on selected enrollments
         */
        performBulkAction: function(action, enrollmentIds) {
            // Show loading state
            $('#apply-bulk-action').prop('disabled', true);
            
            // Simulate bulk action
            setTimeout(function() {
                var message = '';
                
                switch(action) {
                    case 'enroll':
                        message = 'Enrolled ' + enrollmentIds.length + ' students successfully!';
                        break;
                    case 'unenroll':
                        message = 'Unenrolled ' + enrollmentIds.length + ' students successfully!';
                        break;
                    case 'delete':
                        message = 'Deleted ' + enrollmentIds.length + ' enrollments successfully!';
                        break;
                    default:
                        message = 'Bulk action completed on ' + enrollmentIds.length + ' enrollments.';
                }
                
                VedMGClassRoomAdmin.showMessage(message, 'success');
                
                // Clear selections
                $('.enrollment-checkbox').prop('checked', false);
                $('#select-all-enrollments').prop('checked', false);
                
                // Re-enable bulk action button
                $('#apply-bulk-action').prop('disabled', false);
                
                // Update counts
                VedMGEnrollments.updateEnrollmentCounts();
                
            }, 2000);
        },
        
        /**
         * Update bulk action button state
         */
        updateBulkActionState: function() {
            var selectedCount = $('.enrollment-checkbox:checked').length;
            var $bulkBtn = $('#apply-bulk-action');
            
            if (selectedCount > 0) {
                $bulkBtn.prop('disabled', false);
                $bulkBtn.text('Apply (' + selectedCount + ')');
            } else {
                $bulkBtn.prop('disabled', true);
                $bulkBtn.text('Apply');
            }
        },
        
        /**
         * Refresh enrollments data
         */
        refreshEnrollmentsData: function() {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'refresh_enrollments',
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Sync with WooCommerce
         */
        syncWithWooCommerce: function() {
            console.log('=== syncWithWooCommerce Function Called ===');
            
            return new Promise(function(resolve, reject) {
                console.log('Creating AJAX request...');
                
                var ajaxData = {
                    action: 'vedmg_classroom_action',
                    action_type: 'sync_woocommerce',
                    nonce: vedmg_classroom_ajax.nonce
                };
                
                console.log('AJAX data being sent:', ajaxData);
                console.log('AJAX URL:', vedmg_classroom_ajax.ajax_url);
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: ajaxData,
                    beforeSend: function() {
                        console.log('AJAX request starting...');
                    },
                    success: function(response) {
                        console.log('=== AJAX Success Response ===');
                        console.log('Raw response:', response);
                        console.log('Response type:', typeof response);
                        
                        if (response.success) {
                            console.log('✓ Server reported success');
                            console.log('Success data:', response.data);
                            resolve(response.data);
                        } else {
                            console.log('❌ Server reported failure');
                            console.log('Error data:', response.data);
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('=== AJAX Error ===');
                        console.log('XHR object:', xhr);
                        console.log('Status:', status);
                        console.log('Error:', error);
                        console.log('Response Text:', xhr.responseText);
                        console.log('Status Code:', xhr.status);
                        
                        var errorMessage = 'AJAX Error: ' + status;
                        if (error) {
                            errorMessage += ' - ' + error;
                        }
                        if (xhr.responseText) {
                            errorMessage += ' - Response: ' + xhr.responseText;
                        }
                        
                        reject({
                            message: errorMessage,
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            });
        },
        
        /**
         * Fetch classroom data from Google Classroom API
         */
        fetchClassroomData: function() {
            console.log('=== fetchClassroomData Function Called ===');
            
            return new Promise(function(resolve, reject) {
                console.log('Creating AJAX request...');
                
                var ajaxData = {
                    action: 'vedmg_classroom_action',
                    action_type: 'fetch_classroom_data',
                    nonce: vedmg_classroom_ajax.nonce
                };
                
                console.log('AJAX data being sent:', ajaxData);
                console.log('AJAX URL:', vedmg_classroom_ajax.ajax_url);
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: ajaxData,
                    beforeSend: function() {
                        console.log('AJAX request starting...');
                    },
                    success: function(response) {
                        console.log('=== AJAX Success Response ===');
                        console.log('Raw response:', response);
                        console.log('Response type:', typeof response);
                        
                        if (response.success) {
                            console.log('✓ Server reported success');
                            console.log('Success data:', response.data);
                            resolve(response.data);
                        } else {
                            console.log('❌ Server reported failure');
                            console.log('Error data:', response.data);
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('=== AJAX Error ===');
                        console.log('XHR object:', xhr);
                        console.log('Status:', status);
                        console.log('Error:', error);
                        console.log('Response Text:', xhr.responseText);
                        console.log('Status Code:', xhr.status);
                        
                        var errorMessage = 'AJAX Error: ' + status;
                        if (error) {
                            errorMessage += ' - ' + error;
                        }
                        if (xhr.responseText) {
                            errorMessage += ' - Response: ' + xhr.responseText;
                        }
                        
                        reject({
                            message: errorMessage,
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            });
        },
        
        /**
         * Schedule lab session
         */
        scheduleLabSession: function(formData) {
            console.log('=== scheduleLabSession Function Called ===');
            
            return new Promise(function(resolve, reject) {
                console.log('Creating AJAX request...');
                
                var ajaxData = {
                    action: 'vedmg_classroom_action',
                    action_type: 'schedule_lab_session',
                    nonce: vedmg_classroom_ajax.nonce,
                    form_data: formData
                };
                
                console.log('AJAX data being sent:', ajaxData);
                console.log('AJAX URL:', vedmg_classroom_ajax.ajax_url);
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: ajaxData,
                    beforeSend: function() {
                        console.log('AJAX request starting...');
                    },
                    success: function(response) {
                        console.log('=== AJAX Success Response ===');
                        console.log('Raw response:', response);
                        console.log('Response type:', typeof response);
                        
                        if (response.success) {
                            console.log('✓ Server reported success');
                            console.log('Success data:', response.data);
                            resolve(response.data);
                        } else {
                            console.log('❌ Server reported failure');
                            console.log('Error data:', response.data);
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('=== AJAX Error ===');
                        console.log('XHR object:', xhr);
                        console.log('Status:', status);
                        console.log('Error:', error);
                        console.log('Response Text:', xhr.responseText);
                        console.log('Status Code:', xhr.status);
                        
                        var errorMessage = 'AJAX Error: ' + status;
                        if (error) {
                            errorMessage += ' - ' + error;
                        }
                        if (xhr.responseText) {
                            errorMessage += ' - Response: ' + xhr.responseText;
                        }
                        
                        reject({
                            message: errorMessage,
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            });
        },
        
        /**
         * Update schedule button state after successful scheduling (v2.0)
         */
        updateScheduleButtonState: function(affectedStudents, sessionData) {
            console.log('Updating schedule button state for students:', affectedStudents);

            if (!affectedStudents || !Array.isArray(affectedStudents)) {
                console.warn('No affected students data provided');
                return;
            }

            affectedStudents.forEach(function(studentId) {
                // Find the schedule button for this student
                var $button = $('.vedmg-schedule-lab-btn[data-student-id="' + studentId + '"]');

                if ($button.length > 0) {
                    // Update button appearance and text
                    $button.removeClass('vedmg-classroom-btn-accent')
                           .addClass('vedmg-classroom-btn-success scheduled');

                    // Update button content
                    $button.find('.vedmg-btn-text').text('Reschedule Lab');
                    $button.find('.vedmg-btn-icon').text('🔄');

                    // Add session count if available
                    var sessionCount = sessionData.sessions_created || 1;
                    var $sessionCountSpan = $button.find('.vedmg-session-count');
                    if ($sessionCountSpan.length === 0) {
                        $button.append('<span class="vedmg-session-count">(' + sessionCount + ')</span>');
                    } else {
                        $sessionCountSpan.text('(' + sessionCount + ')');
                    }

                    // Update tooltip
                    $button.attr('title', 'Student has ' + sessionCount + ' scheduled session(s)');
                    $button.attr('data-has-sessions', '1');

                    console.log('Updated button state for student:', studentId);
                } else {
                    console.warn('Schedule button not found for student:', studentId);
                }
            });
        },

        /**
         * Update enrollment counts
         */
        updateEnrollmentCounts: function() {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var pendingCount = 0;
            var enrolledCount = 0;

            $visibleRows.each(function() {
                var status = $(this).find('.vedmg-enrollment-status').attr('data-status');
                if (status === 'pending') {
                    pendingCount++;
                } else if (status === 'enrolled' || status === 'active') {
                    enrolledCount++;
                }
            });
            
            $('#pending-count').text(pendingCount);
            $('#enrolled-count').text(enrolledCount);
            
            // Update statistics section
            $('.vedmg-enrollment-stats .vedmg-stat-item').eq(0).find('.vedmg-stat-value').text($visibleRows.length);
            $('.vedmg-enrollment-stats .vedmg-stat-item').eq(1).find('.vedmg-stat-value').text(enrolledCount);
            $('.vedmg-enrollment-stats .vedmg-stat-item').eq(2).find('.vedmg-stat-value').text(pendingCount);
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        VedMGEnrollments.init();
    });
    
    // Make enrollments object available globally
    window.VedMGEnrollments = VedMGEnrollments;
    
})(jQuery);
