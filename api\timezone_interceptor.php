<?php
/**
 * Timezone Interceptor for VedMG ClassRoom
 *
 * This file intercepts the schedule lab API calls and converts Indian timezone
 * to US timezone before sending to the US-based API server.
 *
 * PROBLEM: API server assumes times are in US timezone, but users input Indian time
 * SOLUTION: Convert IST to US time before API call
 *
 * HOW IT WORKS:
 * 1. Hooks into wp_ajax_vedmg_schedule_lab with priority 5 (before main handler)
 * 2. Detects IST timezone (+05:30) in form submissions
 * 3. Converts Indian time to US Eastern time using PHP DateTime
 * 4. Updates $_POST data with converted times
 * 5. Main handler processes converted times and sends to US API
 * 6. Meeting invites show correct times for all participants
 *
 * EXAMPLE:
 * User Input: 2:00 PM IST → Converted: 3:30 AM EST → API gets EST time → Correct invites
 *
 * TO REMOVE: Simply delete this file and remove the require_once from vedmg-classroom.php
 *
 * @package VedMG_ClassRoom
 * @version 1.0
 * <AUTHOR> Fix
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

class VedMG_Timezone_Interceptor {
    
    /**
     * Initialize the interceptor
     */
    public static function init() {
        // Hook into the schedule lab handler BEFORE it processes
        add_action('wp_ajax_vedmg_schedule_lab', [__CLASS__, 'intercept_schedule_request'], 5);
        add_action('wp_ajax_nopriv_vedmg_schedule_lab', [__CLASS__, 'intercept_schedule_request'], 5);
        
        vedmg_log_info('TIMEZONE_INTERCEPTOR', 'Timezone interceptor initialized');
    }
    
    /**
     * Intercept and convert timezone before the original handler processes
     */
    public static function intercept_schedule_request() {
        try {
            vedmg_log_info('TIMEZONE_INTERCEPTOR', 'Intercepting schedule request for timezone conversion');
            
            // Check if we have timezone data to convert
            if (!isset($_POST['timezone']) || !isset($_POST['session_date']) || !isset($_POST['start_time']) || !isset($_POST['end_time'])) {
                vedmg_log_info('TIMEZONE_INTERCEPTOR', 'No timezone conversion needed - missing required fields');
                return; // Let original handler process normally
            }
            
            $original_timezone = $_POST['timezone'];
            $session_date = $_POST['session_date'];
            $start_time = $_POST['start_time'];
            $end_time = $_POST['end_time'];
            
            vedmg_log_info('TIMEZONE_INTERCEPTOR', 'Original data', [
                'timezone' => $original_timezone,
                'session_date' => $session_date,
                'start_time' => $start_time,
                'end_time' => $end_time
            ]);
            
            // Only convert if timezone is IST (+05:30)
            if ($original_timezone === '+05:30') {

                // Convert IST to compensated time (reverse conversion)
                $converted_times = self::convert_ist_to_compensated_time($session_date, $start_time, $end_time);
                
                if ($converted_times) {
                    // Update $_POST with converted times
                    $_POST['session_date'] = $converted_times['session_date'];
                    $_POST['start_time'] = $converted_times['start_time'];
                    $_POST['end_time'] = $converted_times['end_time'];
                    
                    // Also update end_date if it exists
                    if (isset($_POST['end_date'])) {
                        $_POST['end_date'] = $converted_times['end_date'];
                    }
                    
                    vedmg_log_info('TIMEZONE_INTERCEPTOR', 'Timezone conversion completed', [
                        'original' => [
                            'date' => $session_date,
                            'start' => $start_time,
                            'end' => $end_time,
                            'timezone' => $original_timezone
                        ],
                        'converted' => $converted_times
                    ]);
                } else {
                    vedmg_log_error('TIMEZONE_INTERCEPTOR', 'Failed to convert timezone');
                }
            } else {
                vedmg_log_info('TIMEZONE_INTERCEPTOR', 'No conversion needed - not IST timezone: ' . $original_timezone);
            }
            
        } catch (Exception $e) {
            vedmg_log_error('TIMEZONE_INTERCEPTOR', 'Error in timezone interception', $e->getMessage());
        }
        
        // Continue to original handler (don't stop the process)
        return;
    }
    
    /**
     * Convert IST time to compensated time for US API
     *
     * The US API appears to be doing its own timezone conversion, so we need to
     * send a time that when processed by the API will result in the correct IST time.
     *
     * @param string $date Date in YYYY-MM-DD format
     * @param string $start_time Start time in HH:MM format
     * @param string $end_time End time in HH:MM format
     * @return array|false Converted times or false on failure
     */
    private static function convert_ist_to_compensated_time($date, $start_time, $end_time) {
        try {
            // Create DateTime objects for IST (what user wants)
            $ist_start = new DateTime($date . ' ' . $start_time . ':00', new DateTimeZone('Asia/Kolkata'));
            $ist_end = new DateTime($date . ' ' . $end_time . ':00', new DateTimeZone('Asia/Kolkata'));

            // ADJUSTED FIX: Add 5.5 hours to compensate for the time difference
            // Previous: 9.5 hours → invite showed 6 PM (4 hours too much)
            // New calculation: 9.5 - 4 = 5.5 hours
            // Goal: User wants 2 PM IST, invite should show 2 PM IST

            $compensated_start = clone $ist_start;
            $compensated_end = clone $ist_end;

            // Add 5.5 hours (5.5 * 3600 = 19800 seconds)
            $compensated_start->add(new DateInterval('PT19800S')); // 5.5 hours = 19800 seconds
            $compensated_end->add(new DateInterval('PT19800S'));

            // Extract converted values
            $converted_session_date = $compensated_start->format('Y-m-d');
            $converted_start_time = $compensated_start->format('H:i');
            $converted_end_time = $compensated_end->format('H:i');
            $converted_end_date = $compensated_end->format('Y-m-d');
            
            return [
                'session_date' => $converted_session_date,
                'start_time' => $converted_start_time,
                'end_time' => $converted_end_time,
                'end_date' => $converted_end_date,
                'conversion_info' => [
                    'original_ist_start' => $date . ' ' . $start_time . ' IST',
                    'compensated_time_start' => $converted_session_date . ' ' . $converted_start_time,
                    'original_ist_end' => $date . ' ' . $end_time . ' IST',
                    'compensated_time_end' => $converted_end_date . ' ' . $converted_end_time,
                    'adjustment_applied' => '+5.5 hours (adjusted: 9.5hrs - 4hrs = 5.5hrs)'
                ]
            ];
            
        } catch (Exception $e) {
            vedmg_log_error('TIMEZONE_INTERCEPTOR', 'Error in timezone compensation', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get timezone offset between IST and US Eastern for a specific date
     * (Handles daylight saving time automatically)
     * 
     * @param string $date Date in YYYY-MM-DD format
     * @return array Timezone information
     */
    public static function get_timezone_info($date) {
        try {
            $test_time = new DateTime($date . ' 12:00:00', new DateTimeZone('Asia/Kolkata'));
            $ist_offset = $test_time->getOffset();
            
            $test_time->setTimezone(new DateTimeZone('America/New_York'));
            $us_offset = $test_time->getOffset();
            
            $difference_hours = ($ist_offset - $us_offset) / 3600;
            
            return [
                'ist_offset' => $ist_offset / 3600,
                'us_offset' => $us_offset / 3600,
                'difference_hours' => $difference_hours,
                'is_dst' => $test_time->format('I') == '1'
            ];
            
        } catch (Exception $e) {
            vedmg_log_error('TIMEZONE_INTERCEPTOR', 'Error getting timezone info', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Test function to verify timezone conversion
     * 
     * @param string $test_date Date to test
     * @param string $test_start_time Start time to test
     * @param string $test_end_time End time to test
     * @return array Test results
     */
    public static function test_conversion($test_date, $test_start_time, $test_end_time) {
        $result = [
            'input' => [
                'date' => $test_date,
                'start_time' => $test_start_time,
                'end_time' => $test_end_time,
                'timezone' => 'IST (+05:30)'
            ]
        ];
        
        $converted = self::convert_ist_to_compensated_time($test_date, $test_start_time, $test_end_time);
        
        if ($converted) {
            $result['output'] = $converted;
            $result['success'] = true;
            
            $result['verification'] = [
                'compensated_time_sent_to_api' => $converted['session_date'] . ' ' . $converted['start_time'],
                'expected_result_in_invite' => $test_date . ' ' . $test_start_time . ' IST',
                'adjustment_applied' => $converted['conversion_info']['adjustment_applied']
            ];
        } else {
            $result['success'] = false;
            $result['error'] = 'Conversion failed';
        }
        
        return $result;
    }
}

// Initialize the interceptor
VedMG_Timezone_Interceptor::init();

vedmg_log_info('TIMEZONE_INTERCEPTOR', 'Timezone interceptor file loaded successfully');

?>
