/**
 * VedMG ClassRoom Instructors CSS
 * 
 * Specific styles for the instructor roster page.
 * Contains styles for instructor tables, statistics, and instructor-specific elements.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Instructor Statistics Grid */
.vedmg-instructor-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-instructor-stats-grid .vedmg-stat-card {
    background: #fff;
    padding: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vedmg-instructor-stats-grid .vedmg-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.vedmg-instructor-stats-grid .vedmg-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 8px;
}

.vedmg-instructor-stats-grid .vedmg-stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

/* Instructor Summary */
.vedmg-instructor-summary {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;
    flex-wrap: wrap;
}

.vedmg-instructor-summary strong {
    color: #23282d;
}

/* Instructor Status */
.vedmg-instructor-status[data-status="active"] .vedmg-status-active {
    background: #d4edda;
    color: #155724;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.vedmg-instructor-status[data-status="inactive"] .vedmg-status-inactive {
    background: #f8d7da;
    color: #721c24;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.vedmg-instructor-status[data-status="pending"] .vedmg-status-pending {
    background: #fff3cd;
    color: #856404;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

/* View Courses Link */
.vedmg-view-courses {
    display: block;
    font-size: 12px;
    color: #0073aa;
    text-decoration: none;
    margin-top: 4px;
}

.vedmg-view-courses:hover {
    text-decoration: underline;
}

.vedmg-course-count {
    font-weight: 600;
    color: #23282d;
}

/* Instructor Action Buttons */
.vedmg-view-instructor-btn {
    background: #17a2b8;
    border-color: #17a2b8;
    font-size: 12px;
    padding: 6px 12px;
}

.vedmg-view-instructor-btn:hover {
    background: #138496;
    border-color: #117a8b;
}

.vedmg-edit-instructor-btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* Instructor Search */
.vedmg-filter-input[type="text"] {
    min-width: 200px;
}

/* Recent Activity */
.vedmg-recent-activity {
    margin-top: 15px;
}

.vedmg-activity-item {
    display: flex;
    gap: 15px;
    padding: 12px;
    border-bottom: 1px solid #e5e5e5;
    align-items: center;
}

.vedmg-activity-item:last-child {
    border-bottom: none;
}

.vedmg-activity-time {
    font-size: 12px;
    color: #999;
    font-weight: 500;
    min-width: 80px;
    flex-shrink: 0;
}

.vedmg-activity-text {
    font-size: 14px;
    color: #555;
    line-height: 1.4;
}

/* Instructor Checkboxes */
.instructor-checkbox {
    margin: 0;
    transform: scale(1.1);
}

#select-all-instructors {
    margin: 0;
    transform: scale(1.1);
}

/* Placeholder Data Styling */
.placeholder-data {
    color: #999 !important;
    font-style: italic;
}

/* Add Instructor Button */
#add-instructor {
    background: #28a745;
    border-color: #28a745;
}

#add-instructor:hover {
    background: #218838;
    border-color: #1e7e34;
}

/* Sync Buttons */
#sync-masterstudy {
    background: #6c757d;
    border-color: #6c757d;
}

#sync-masterstudy:hover {
    background: #5a6268;
    border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-instructor-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .vedmg-instructor-summary {
        flex-direction: column;
        gap: 8px;
    }
    
    .vedmg-activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .vedmg-activity-time {
        min-width: auto;
        font-weight: 600;
    }
    
    .vedmg-filter-input[type="text"] {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .vedmg-instructor-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-instructor-stats-grid .vedmg-stat-card {
        padding: 15px;
    }
    
    .vedmg-instructor-stats-grid .vedmg-stat-number {
        font-size: 24px;
    }
    
    .vedmg-action-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .vedmg-action-buttons .vedmg-classroom-btn {
        text-align: center;
        padding: 8px 12px;
    }
    
    .vedmg-activity-item {
        padding: 10px;
    }
    
    .vedmg-activity-text {
        font-size: 13px;
    }
}

/* Search Form Styles */
.vedmg-search-form {
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vedmg-search-controls {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.vedmg-search-group {
    display: flex;
    flex-direction: column;
}

.vedmg-search-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-search-input,
.vedmg-search-select {
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.vedmg-search-input:focus,
.vedmg-search-select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-search-input {
    width: 100%;
}

/* Search form responsive design */
@media (max-width: 768px) {
    .vedmg-search-controls {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .vedmg-search-group:last-child {
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
    }
    
    .vedmg-search-group:last-child label {
        display: none;
    }
}

/* Sessions Sections Styling */
.vedmg-sessions-section {
    margin-bottom: 25px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    overflow: hidden;
}

.vedmg-sessions-section-title {
    background: #f8f9fa;
    margin: 0;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e5e5e5;
}

.vedmg-upcoming-class-item {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.vedmg-upcoming-class-item:last-child {
    border-bottom: none;
}

.vedmg-upcoming-class-item:hover {
    background-color: #f8f9fa;
}

.vedmg-upcoming-class-item.current {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.vedmg-upcoming-class-item.future {
    background-color: #ffffff;
}

.vedmg-upcoming-class-info {
    padding: 12px 15px;
}

.vedmg-upcoming-class-date {
    font-weight: 600;
    color: #0073aa;
    font-size: 13px;
    margin-bottom: 4px;
}

.vedmg-upcoming-class-time {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
}

.vedmg-upcoming-class-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    font-size: 14px;
}

.vedmg-upcoming-class-course {
    font-size: 12px;
    color: #888;
    margin-bottom: 4px;
}

.vedmg-upcoming-class-students {
    font-size: 11px;
    color: #666;
    margin-bottom: 6px;
}

.vedmg-upcoming-class-link {
    margin-top: 6px;
}

.vedmg-upcoming-class-link a {
    color: #0073aa;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
}

.vedmg-upcoming-class-link a:hover {
    text-decoration: underline;
}

.vedmg-recurring-indicator {
    font-size: 11px;
    color: #28a745;
    font-weight: 500;
    margin-top: 4px;
}
