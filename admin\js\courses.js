/**
 * VedMG ClassRoom Courses JavaScript
 * 
 * JavaScript functionality specific to the course management page.
 * Handles course actions, Google Classroom integration, and course management.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

(function($) {
    'use strict';

    /**
     * Course management specific functionality
     */
    var VedMGCourses = {
        
        /**
         * Initialize course management functionality
         */
        init: function() {
            console.log('VedMG ClassRoom Courses initialized');
            
            // Bind course specific events
            this.bindEvents();
            
            // Initialize course components
            this.initComponents();
        },
        
        /**
         * Bind course specific events
         */
        bindEvents: function() {
            // Course management actions
            $(document).on('click', '.vedmg-create-classroom-btn', this.handleCreateClassroom);
            $(document).on('click', '.vedmg-manage-classroom-btn', this.handleManageClassroom);
            $(document).on('click', '.vedmg-view-course-btn', this.handleViewCourse);
            $(document).on('click', '.vedmg-delete-course-btn', this.handleDeleteCourse);
            $(document).on('click', '.vedmg-meeting-link-btn', this.handleMeetingLink);
            
            // Modal events
            $(document).on('click', '.vedmg-modal-close, #cancel-meeting-link', this.closeMeetingModal);
            $(document).on('submit', '#vedmg-meeting-link-form', this.handleSaveMeetingLink);
            
            // Close modal when clicking outside
            $(document).on('click', '#vedmg-meeting-link-modal', function(e) {
                if (e.target === this) {
                    VedMGCourses.closeMeetingModal();
                }
            });
            
            // Refresh and sync actions
            $(document).on('click', '#refresh-courses', this.handleRefreshCourses);

            $(document).on('click', '#sync-masterstudy', this.handleSyncMasterStudy);
            
            // Course table interactions
            $(document).on('click', '.vedmg-view-students', this.handleViewStudents);
            
            console.log('Course events bound');
        },
        
        /**
         * Initialize course components
         */
        initComponents: function() {
            // Initialize course table
            this.initCourseTable();
            
            // Initialize course statistics
            this.updateCourseStats();
            
            console.log('Course components initialized');
        },
        
        /**
         * Initialize course table
         */
        initCourseTable: function() {
            // Add hover effects and interactions
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var courseId = $row.data('course-id');
                
                if (courseId) {
                    $row.attr('data-course-id', courseId);
                }
            });
        },
        
        /**
         * Handle create classroom action
         */
        handleCreateClassroom: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var courseId = $button.data('course-id');
            var courseName = $button.closest('tr').find('td:first strong').text();
            
            // Confirm action
            var confirmed = confirm('Create Google Classroom for "' + courseName + '"?\n\nThis will create a new Google Classroom and link it to this course.');
            
            if (!confirmed) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Creating...');
            
            // Simulate classroom creation (placeholder)
            setTimeout(function() {
                VedMGCourses.createClassroomForCourse(courseId, courseName, $button);
            }, 2000);
            
            console.log('Creating classroom for course:', courseId);
        },
        
        /**
         * Handle manage classroom action
         */
        handleManageClassroom: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var courseId = $button.data('course-id');
            var courseName = $button.closest('tr').find('td:first strong').text();
            
            // Open classroom management modal or redirect
            VedMGCourses.openClassroomManagement(courseId, courseName);
            
            console.log('Managing classroom for course:', courseId);
        },
        
        /**
         * Handle view course action
         */
        handleViewCourse: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var courseId = $button.data('course-id');
            var courseName = $button.closest('tr').find('td:first strong').text();
            
            // Open course view modal
            VedMGCourses.openCourseViewer(courseId, courseName);
            
            console.log('Viewing course:', courseId, 'Name:', courseName);
        },
        
        /**
         * Handle edit course action (kept for backward compatibility but now opens view)
         */
        handleEditCourse: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var courseId = $button.data('course-id');
            var courseName = $button.closest('tr').find('td:first strong').text();
            
            // Open course view modal instead of edit
            VedMGCourses.openCourseViewer(courseId, courseName);
            
            console.log('Viewing course:', courseId, 'Name:', courseName);
        },
        
        /**
         * Handle delete course action
         */
        handleDeleteCourse: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var courseId = $button.data('course-id');
            var courseName = $button.closest('tr').find('td:first strong').text();
            
            // Confirm deletion
            var confirmed = confirm('Are you sure you want to delete "' + courseName + '"?\n\nThis action cannot be undone and will remove all associated data.');
            
            if (!confirmed) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Deleting...');
            
            // Perform deletion
            VedMGCourses.deleteCourse(courseId, $button);
            
            console.log('Deleting course:', courseId);
        },
        
        /**
         * Handle refresh courses action
         */
        handleRefreshCourses: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $spinner = $button.find('.vedmg-classroom-spinner');
            
            // Show loading state
            $button.addClass('loading');
            $button.prop('disabled', true);
            
            // Refresh courses data
            VedMGCourses.refreshCoursesData().then(function() {
                // Hide loading state
                $button.removeClass('loading');
                $button.prop('disabled', false);
                
                // Show success message
                VedMGClassRoomAdmin.showMessage('Courses refreshed successfully!', 'success');
            }).catch(function(error) {
                console.error('Failed to refresh courses:', error);
                
                // Hide loading state
                $button.removeClass('loading');
                $button.prop('disabled', false);
                
                // Show error message
                VedMGClassRoomAdmin.showMessage('Failed to refresh courses. Please try again.', 'error');
            });
        },
        

        
        /**
         * Handle sync with MasterStudy LMS
         */
        handleSyncMasterStudy: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            
            // Confirm sync
            var confirmed = confirm('Sync with MasterStudy LMS?\n\nThis will fetch the latest course data from MasterStudy LMS and update the database.');
            
            if (!confirmed) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Syncing...');
            
            // Perform sync
            VedMGCourses.syncWithMasterStudy().then(function(result) {
                $button.prop('disabled', false);
                $button.text('Sync with MasterStudy LMS');
                
                VedMGClassRoomAdmin.showMessage('Sync completed! ' + result.message, 'success');
                
                // Refresh the page to show updated data
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
                
            }).catch(function(error) {
                console.error('Sync failed:', error);
                
                $button.prop('disabled', false);
                $button.text('Sync with MasterStudy LMS');
                
                VedMGClassRoomAdmin.showMessage('Sync failed. Please try again.', 'error');
            });
        },
        
        /**
         * Handle view students action
         */
        handleViewStudents: function(e) {
            e.preventDefault();
            
            var href = $(this).attr('href');
            
            if (href && href !== '#') {
                window.location.href = href;
            } else {
                // Fallback for placeholder links
                var courseId = $(this).closest('tr').data('course-id');
                var courseName = $(this).closest('tr').find('td:first strong').text();
                
                VedMGClassRoomAdmin.showMessage('Viewing students for "' + courseName + '" (Placeholder action)', 'info');
            }
        },
        
        /**
         * Create classroom for course
         */
        createClassroomForCourse: function(courseId, courseName, $button) {
            // Use API to create Google Classroom
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_create_google_classroom',
                    course_id: courseId,
                    nonce: vedmg_classroom_ajax.api_nonce
                },
                success: function(response) {
                    $button.prop('disabled', false);
                    
                    if (response.success) {
                        // Update button state
                        $button.removeClass('vedmg-create-classroom-btn');
                        $button.addClass('vedmg-manage-classroom-btn');
                        $button.text('Manage Classroom');
                        
                        // Update status in the row
                        var $statusCell = $button.closest('tr').find('.vedmg-classroom-status');
                        $statusCell.html('<span class="vedmg-status-active">Active</span>');
                        
                        // Show success message
                        VedMGClassRoomAdmin.showMessage('Google Classroom created successfully for "' + courseName + '"!', 'success');
                        
                        // Update statistics
                        VedMGCourses.updateCourseStats();
                    } else {
                        $button.text('Create Classroom');
                        VedMGClassRoomAdmin.showMessage('Failed to create Google Classroom: ' + response.data, 'error');
                    }
                },
                error: function() {
                    $button.prop('disabled', false);
                    $button.text('Create Classroom');
                    VedMGClassRoomAdmin.showMessage('Failed to create Google Classroom. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Open classroom management interface
         */
        openClassroomManagement: function(courseId, courseName) {
            // In real implementation, this would open a modal or redirect to management page
            
            var message = 'Opening classroom management for "' + courseName + '"\n\n';
            message += 'Available actions:\n';
            message += '• Manage student enrollments\n';
            message += '• Schedule class sessions\n';
            message += '• Configure classroom settings\n';
            message += '• View classroom analytics';
            
            // Show management modal
            VedMGCourses.showManagementModal(courseId, courseName);
        },
        
        /**
         * Open course viewer (read-only modal)
         */
        openCourseViewer: function(courseId, courseName) {
            // Create and show course viewer modal
            VedMGCourses.showCourseViewerModal(courseId, courseName);
        },
        
        /**
         * Open course editor
         */
        openCourseEditor: function(courseId, courseName) {
            // Create and show course editor modal
            VedMGCourses.showCourseEditorModal(courseId, courseName);
        },
        
        /**
         * Delete course
         */
        deleteCourse: function(courseId, $button) {
            // Use API to delete course from database
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_delete_course',
                    course_id: courseId,
                    nonce: vedmg_classroom_ajax.api_nonce
                },
                success: function(response) {
                    $button.prop('disabled', false);
                    $button.text('Delete');
                    
                    if (response.success) {
                        // Remove the row from the table
                        var $row = $button.closest('tr');
                        $row.fadeOut(500, function() {
                            $row.remove();
                            
                            // Update statistics
                            VedMGCourses.updateCourseStats();
                            
                            // Show success message
                            VedMGClassRoomAdmin.showMessage('Course deleted successfully from database.', 'success');
                        });
                    } else {
                        VedMGClassRoomAdmin.showMessage('Failed to delete course: ' + response.data, 'error');
                    }
                },
                error: function() {
                    $button.prop('disabled', false);
                    $button.text('Delete');
                    VedMGClassRoomAdmin.showMessage('Failed to delete course. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Refresh courses data
         */
        refreshCoursesData: function() {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'refresh_courses',
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        

        
        /**
         * Sync with MasterStudy LMS
         */
        syncWithMasterStudy: function() {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'sync_masterstudy',
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Update course statistics
         */
        updateCourseStats: function() {
            var totalCourses = $('.vedmg-classroom-table tbody tr[data-course-id]').length;
            var activeCourses = $('.vedmg-status-active').length;
            var pendingCourses = $('.vedmg-status-pending').length;
            
            // Update statistics display
            $('.vedmg-course-stats .vedmg-stat-item').eq(0).find('.vedmg-stat-value').text(totalCourses);
            $('.vedmg-course-stats .vedmg-stat-item').eq(1).find('.vedmg-stat-value').text(activeCourses);
            $('.vedmg-course-stats .vedmg-stat-item').eq(2).find('.vedmg-stat-value').text(pendingCourses);
        },
        
        /**
         * Handle meeting link button click
         */
        handleMeetingLink: function(e) {
            e.preventDefault();

            var $button = $(this);
            var courseId = $button.data('course-id');
            var hasLink = $button.data('has-link') === true || $button.data('has-link') === 'true';
            var $row = $button.closest('tr');
            var courseName = $row.find('td:first strong').text();
            var instructorName = $row.find('td:nth-child(2)').text();

            // Open meeting link modal with appropriate mode
            VedMGCourses.openMeetingModal(courseId, courseName, instructorName, hasLink);

            console.log('Opening meeting link modal for course:', courseId, 'hasLink:', hasLink);
        },
        
        /**
         * Open meeting link modal
         */
        openMeetingModal: function(courseId, courseName, instructorName, hasLink) {
            console.log('🔓 Opening meeting modal for course:', courseId, courseName, 'hasLink:', hasLink);

            // Store the hasLink state for later use
            VedMGCourses.currentCourseHasLink = hasLink || false;

            // Populate form with course data
            $('#meeting-course-id').val(courseId);
            $('#meeting-title').val('Class Session - ' + courseName);
            $('#meeting-description').val('Online class session for ' + courseName + ' conducted by ' + instructorName);

            // Update modal title and button text based on whether link exists
            if (hasLink) {
                $('#meeting-modal-title').text('View/Update Meeting Link');
                $('#save-meeting-link-text').text('Update Meeting Link');
                $('#meeting-link-edit-section').show();
            } else {
                $('#meeting-modal-title').text('Save Meeting Link');
                $('#save-meeting-link-text').text('Save Meeting Link');
                $('#meeting-link-edit-section').hide();
            }

            // Load teachers into dropdown and set default selection
            VedMGCourses.loadTeachersDropdown(courseId);

            // Fetch course data to populate existing meeting link if it exists
            VedMGCourses.fetchCourseDataAndUpdateLinkField(courseId);

            // Show modal
            $('#vedmg-meeting-link-modal').fadeIn(300, function() {
                console.log('✅ Modal is now visible');

                // Test if form exists
                var $form = $('#vedmg-meeting-link-form');
                console.log('📝 Form element found:', $form.length > 0);
                console.log('📝 Form element:', $form);

                // Test if save button exists
                var $saveBtn = $('#save-meeting-link');
                console.log('🔘 Save button found:', $saveBtn.length > 0);
                console.log('🔘 Save button:', $saveBtn);
            });
        },

        /**
         * Fetch course data and update link field behavior
         */
        fetchCourseDataAndUpdateLinkField: function(courseId) {
            console.log('📡 Fetching course data for link field logic, course ID:', courseId);
            console.log('🔧 AJAX URL:', vedmg_classroom_ajax.ajax_url);
            console.log('🔧 Nonce:', vedmg_classroom_ajax.nonce);

            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_get_course_data',
                    course_id: courseId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('📡 AJAX Response received:', response);
                    if (response.success) {
                        console.log('✅ Course data fetched successfully:', response.data);
                        VedMGCourses.updateMeetingLinkField(response.data);
                    } else {
                        console.error('❌ Failed to fetch course data:', response.data);
                        console.log('🔄 Using fallback - enabling field for manual entry');
                        // Fallback to default behavior (enable field)
                        VedMGCourses.updateMeetingLinkField({});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error fetching course data:', error);
                    console.error('❌ XHR:', xhr);
                    console.error('❌ Status:', status);
                    console.log('🔄 Using fallback - enabling field for manual entry');
                    // Fallback to default behavior (enable field)
                    VedMGCourses.updateMeetingLinkField({});
                }
            });
        },

        /**
         * Update meeting link field based on course data
         */
        updateMeetingLinkField: function(courseData) {
            console.log('🔧 Updating meeting link field with course data:', courseData);

            var $linkField = $('#meeting-link');
            var $linkLabel = $('label[for="meeting-link"]');
            var $editSection = $('#meeting-link-edit-section');
            var $editCheckbox = $('#enable-meeting-edit');

            console.log('🔍 DOM Elements found:', {
                linkField: $linkField.length,
                linkLabel: $linkLabel.length,
                editSection: $editSection.length,
                editCheckbox: $editCheckbox.length
            });

            // Check if course has a meeting link
            var hasMeetingLink = courseData.meeting_link && courseData.meeting_link.trim() !== '';

            console.log('🔍 Meeting link analysis:', {
                meeting_link: courseData.meeting_link,
                meeting_link_type: typeof courseData.meeting_link,
                hasMeetingLink: hasMeetingLink,
                currentCourseHasLink: VedMGCourses.currentCourseHasLink
            });

            // Remove any existing info messages
            $('#api-link-info').remove();

            if (hasMeetingLink) {
                // Course has a meeting link - show in read-only mode with edit option
                console.log('🔒 Course has meeting link - showing in read-only mode');
                console.log('🔧 Setting field value to:', courseData.meeting_link);

                $linkField.prop('readonly', true)
                         .addClass('readonly')
                         .val(courseData.meeting_link)
                         .attr('placeholder', '');

                console.log('🔍 Field value after setting:', $linkField.val());
                console.log('🔍 Field properties:', {
                    readonly: $linkField.prop('readonly'),
                    hasClass: $linkField.hasClass('readonly'),
                    placeholder: $linkField.attr('placeholder')
                });

                $linkLabel.text('Meeting Link:');
                $editSection.show();

                // Reset edit checkbox
                $editCheckbox.prop('checked', false);

                // Add info message
                var linkDisplay = courseData.meeting_link.length > 60 ?
                    courseData.meeting_link.substring(0, 60) + '...' :
                    courseData.meeting_link;

                $linkField.after('<p id="api-link-info" class="description" style="color: #0073aa; font-style: italic; margin-top: 5px;">' +
                               '<strong>Current Meeting Link:</strong> <a href="' + courseData.meeting_link + '" target="_blank" style="color: #0073aa;">' + linkDisplay + '</a><br>' +
                               '<em>Check the box below to edit this link.</em></p>');

            } else {
                // No meeting link - enable field for entry
                console.log('🔓 No meeting link - enabling field for entry');

                $linkField.prop('readonly', false)
                         .removeClass('readonly')
                         .val('')
                         .attr('placeholder', 'Enter Google Meet, Zoom, or other meeting platform link');

                $linkLabel.text('Meeting Link:');
                $editSection.hide();
            }

            // Set up edit checkbox functionality
            $editCheckbox.off('change').on('change', function() {
                var isChecked = $(this).is(':checked');
                console.log('📝 Edit checkbox changed:', isChecked);

                if (isChecked) {
                    // Enable editing
                    $linkField.prop('readonly', false).removeClass('readonly');
                    $('#api-link-info').html('<em style="color: #d63638;">Editing mode enabled. You can now modify the meeting link.</em>');
                } else {
                    // Disable editing - restore original value
                    $linkField.prop('readonly', true).addClass('readonly').val(courseData.meeting_link);
                    var linkDisplay = courseData.meeting_link.length > 60 ?
                        courseData.meeting_link.substring(0, 60) + '...' :
                        courseData.meeting_link;
                    $('#api-link-info').html('<strong>Current Meeting Link:</strong> <a href="' + courseData.meeting_link + '" target="_blank" style="color: #0073aa;">' + linkDisplay + '</a><br>' +
                                           '<em>Check the box below to edit this link.</em>');
                }
            });
        },

        /**
         * Load teachers into dropdown
         */
        loadTeachersDropdown: function(courseId) {
            console.log('👨‍🏫 Loading teachers into dropdown for course:', courseId);
            
            var $dropdown = $('#assigned-teacher');
            console.log('📋 Dropdown element found:', $dropdown.length > 0);
            
            // Show loading state
            $dropdown.html('<option value="">Loading teachers...</option>');
            
            // Make AJAX call to get teachers AND course creator info
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_course_creator_and_instructors',
                    course_id: courseId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('👨‍🏫 Course creator and teachers AJAX response:', response);
                    
                    if (response.success) {
                        var options = '';
                        
                        // Add course creator as the only option (pre-selected and disabled)
                        if (response.data.course_creator) {
                            options += '<option value="' + response.data.course_creator.id + '" selected>' + 
                                      response.data.course_creator.name + ' (Course Creator)' + 
                                      '</option>';
                            // Store course creator ID for form submission
                            VedMGCourses.courseCreatorId = response.data.course_creator.id;
                            console.log('📌 Course creator set as default:', response.data.course_creator.name, 'ID:', response.data.course_creator.id);
                        } else {
                            options = '<option value="">No course creator found</option>';
                        }
                        
                        $dropdown.html(options);
                        // Make the dropdown disabled since only course creator should be assigned
                        $dropdown.prop('disabled', true);
                        console.log('✅ Loaded course creator and disabled dropdown');
                    } else {
                        $dropdown.html('<option value="">No teachers available</option>');
                        console.log('❌ No instructors found in response:', response);
                    }
                },
                error: function(xhr, status, error) {
                    $dropdown.html('<option value="">Failed to load teachers</option>');
                    console.error('💥 Failed to load teachers:', error, xhr.responseText);
                }
            });
        },
        
        /**
         * Close meeting modal
         */
        closeMeetingModal: function() {
            $('#vedmg-meeting-link-modal').fadeOut(300);
            $('#vedmg-meeting-link-form')[0].reset();
        },
        
        /**
         * Handle save meeting link
         */
        handleSaveMeetingLink: function(e) {
            e.preventDefault();
            console.log('🔧 Meeting link form submitted');
            
            var $form = $(this);
            var $saveBtn = $('#save-meeting-link');
            var $spinner = $saveBtn.find('.vedmg-classroom-spinner');
            
            console.log('📋 Form element:', $form);
            console.log('🔘 Save button:', $saveBtn);
            
            // Validate form
            if (!VedMGCourses.validateMeetingForm()) {
                console.log('❌ Form validation failed');
                return;
            }
            
            console.log('✅ Form validation passed');
            
            // Show loading state
            $saveBtn.prop('disabled', true);
            $saveBtn.text('Generating Meeting...');
            $spinner.show();
            
            // Get current date and time for database storage
            var now = new Date();
            var currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format
            var currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS format
            
            // Get form data
            var assignedTeacher = $('#assigned-teacher').val();
            var formData = {
                course_id: $('#meeting-course-id').val(),
                meeting_title: $('#meeting-title').val(),
                meeting_description: $('#meeting-description').val(),
                meeting_link: $('#meeting-link').val(),
                meeting_date: currentDate, // Use current date
                meeting_time: currentTime, // Use current time
                assigned_teacher: assignedTeacher || (VedMGCourses.courseCreatorId ? VedMGCourses.courseCreatorId : 'course_creator')
            };
            
            console.log('📤 Form data to submit:', formData);
            console.log('🔍 Using current date/time:', currentDate, currentTime);
            console.log('🔍 Assigned teacher selected:', assignedTeacher);
            console.log('🔍 Course creator ID stored:', VedMGCourses.courseCreatorId);
            console.log('🔍 Final assigned_teacher value:', formData.assigned_teacher);
            
            // Submit to backend
            VedMGCourses.saveMeetingLink(formData).then(function(response) {
                // Success
                console.log('✅ Meeting link saved successfully:', response);

                $saveBtn.prop('disabled', false);
                $spinner.hide();

                // Show appropriate success message based on whether this was first save or update
                var isUpdate = VedMGCourses.currentCourseHasLink;
                var successMessage = isUpdate ? 'Link updated' : 'Meeting saved';
                var buttonText = isUpdate ? 'Link Updated!' : 'Link Saved!';

                $('#save-meeting-link-text').text(buttonText);
                VedMGClassRoomAdmin.showMessage(successMessage, 'success');

                // Keep modal open for a moment to show success, then close and refresh
                setTimeout(function() {
                    VedMGCourses.closeMeetingModal();

                    // Refresh the page to show updated button state
                    console.log('🔄 Refreshing page to show updated meeting link state');
                    window.location.reload();
                }, 2000);
                
            }).catch(function(error) {
                // Error
                console.error('❌ Failed to assign meeting link:', error);
                
                $saveBtn.prop('disabled', false);
                $saveBtn.text('Assign Meeting to Teacher');
                $spinner.hide();
                
                VedMGClassRoomAdmin.showMessage('Failed to assign meeting link. Please try again.', 'error');
            });
        },
        
        /**
         * Validate meeting form
         */
        validateMeetingForm: function() {
            var isValid = true;
            var errors = [];
            
            // Check required fields - only meeting link is required now
            if (!$('#meeting-link').val().trim()) {
                errors.push('Meeting link is required');
                isValid = false;
            }
            
            // Validate URL format
            var urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
            if ($('#meeting-link').val().trim() && !urlPattern.test($('#meeting-link').val().trim())) {
                errors.push('Please enter a valid meeting link URL');
                isValid = false;
            }
            
            // Show errors if any
            if (!isValid) {
                VedMGClassRoomAdmin.showMessage('Please fix the following errors:\n\n' + errors.join('\n'), 'error');
            }
            
            return isValid;
        },
        
        /**
         * Save meeting link to backend
         */
        saveMeetingLink: function(formData) {
            console.log('🚀 Starting AJAX call to save meeting link');
            console.log('📡 AJAX URL:', vedmg_classroom_ajax.ajax_url);
            console.log('🔐 API Nonce:', vedmg_classroom_ajax.api_nonce);
            
            return new Promise(function(resolve, reject) {
                var ajaxData = {
                    action: 'vedmg_save_meeting_link',
                    course_id: formData.course_id,
                    meeting_link: formData.meeting_link,
                    meeting_title: formData.meeting_title,
                    meeting_description: formData.meeting_description,
                    meeting_date: formData.meeting_date,
                    meeting_time: formData.meeting_time,
                    assigned_teacher: formData.assigned_teacher,
                    nonce: vedmg_classroom_ajax.api_nonce
                };
                
                console.log('📤 AJAX Data being sent:', ajaxData);
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: ajaxData,
                    success: function(response) {
                        console.log('📥 AJAX Response received:', response);
                        
                        if (response.success) {
                            console.log('✅ AJAX call successful');
                            resolve(response.data);
                        } else {
                            console.log('❌ AJAX call failed with response:', response.data);
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('💥 AJAX call error:');
                        console.log('   Status:', status);
                        console.log('   Error:', error);
                        console.log('   Response Text:', xhr.responseText);
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Update course row with meeting link
         */
        updateCourseRow: function(courseId, meetingLink) {
            var $row = $('tr[data-course-id="' + courseId + '"]');
            if ($row.length) {
                // Add visual indicator that meeting link was generated
                var $meetingBtn = $row.find('.vedmg-meeting-link-btn');
                $meetingBtn.removeClass('vedmg-classroom-btn-warning').addClass('vedmg-classroom-btn-success');
                $meetingBtn.text('Update Meeting Link');
                
                // Show success message on the row temporarily
                var $successIndicator = $('<span class="vedmg-success-indicator">✓ Meeting link generated</span>');
                $meetingBtn.after($successIndicator);
                
                setTimeout(function() {
                    $successIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        },
        
        /**
         * Show management modal for course
         */
        showManagementModal: function(courseId, courseName) {
            // Create management modal if it doesn't exist
            if (!$('#vedmg-management-modal').length) {
                VedMGCourses.createManagementModal();
            }
            
            // Populate modal with course data
            $('#management-course-id').val(courseId);
            $('#management-course-name').text(courseName);
            
            // Show modal
            $('#vedmg-management-modal').fadeIn(300);
        },
        
        /**
         * Show course viewer modal (read-only)
         */
        showCourseViewerModal: function(courseId, courseName) {
            // Create viewer modal if it doesn't exist
            if (!$('#vedmg-viewer-modal').length) {
                VedMGCourses.createViewerModal();
            }

            // Load course data and populate view
            VedMGCourses.loadCourseDataForView(courseId).then(function(courseData) {
                $('#viewer-course-name').text(courseData.course_name || courseName);
                $('#viewer-course-description').text(courseData.course_description || 'No description available');
                $('#viewer-instructor-name').text(courseData.instructor_name || 'Unknown Instructor');
                $('#viewer-classroom-status').text(courseData.classroom_status || 'pending');
                $('#viewer-google-classroom-id').text(courseData.google_classroom_id || 'Not created');
                $('#viewer-created-date').text(courseData.created_date || 'Unknown');
                $('#viewer-updated-date').text(courseData.updated_date || 'Unknown');
                $('#viewer-student-count').text(courseData.student_count || '0');
                
                // Show modal
                $('#vedmg-viewer-modal').fadeIn(300);
            }).catch(function(error) {
                console.log('Could not load course data, using default values:', error);
                
                // Fallback - use data from the table row or defaults
                var $row = $('tr[data-course-id="' + courseId + '"]');
                var instructorName = '';
                var status = 'pending';
                var studentCount = '0';
                
                if ($row.length) {
                    instructorName = $row.find('td:nth-child(2)').text() || '';
                    var statusElement = $row.find('.vedmg-classroom-status');
                    if (statusElement.length) {
                        var statusData = statusElement.attr('data-status');
                        if (statusData) {
                            status = statusData;
                        }
                    }
                    studentCount = $row.find('.vedmg-student-count').text() || '0';
                }
                
                $('#viewer-course-name').text(courseName);
                $('#viewer-course-description').text('No description available');
                $('#viewer-instructor-name').text(instructorName);
                $('#viewer-classroom-status').text(status);
                $('#viewer-google-classroom-id').text('Not available');
                $('#viewer-created-date').text('Unknown');
                $('#viewer-updated-date').text('Unknown');
                $('#viewer-student-count').text(studentCount);
                
                // Show modal
                $('#vedmg-viewer-modal').fadeIn(300);
            });
        },
        
        /**
         * Show course editor modal
         */
        showCourseEditorModal: function(courseId, courseName) {
            // Create editor modal if it doesn't exist
            if (!$('#vedmg-editor-modal').length) {
                VedMGCourses.createEditorModal();
            }

            // Load course data and populate form
            VedMGCourses.loadCourseData(courseId).then(function(courseData) {
                $('#editor-course-id').val(courseId);
                $('#editor-course-name').val(courseData.course_name || courseName);
                $('#editor-course-description').val(courseData.course_description || '');
                $('#editor-instructor-name').val(courseData.instructor_name || '');
                $('#editor-classroom-status').val(courseData.classroom_status || 'pending');
                
                // Show modal
                $('#vedmg-editor-modal').fadeIn(300);
            }).catch(function(error) {
                console.log('Could not load course data, using default values:', error);
                
                // Fallback - use data from the table row or defaults
                var $row = $('tr[data-course-id="' + courseId + '"]');
                var instructorName = '';
                var status = 'pending';
                
                if ($row.length) {
                    instructorName = $row.find('td:nth-child(2)').text() || '';
                    var statusElement = $row.find('.vedmg-classroom-status');
                    if (statusElement.length) {
                        var statusData = statusElement.attr('data-status');
                        if (statusData) {
                            status = statusData;
                        }
                    }
                }
                
                $('#editor-course-id').val(courseId);
                $('#editor-course-name').val(courseName);
                $('#editor-course-description').val('');
                $('#editor-instructor-name').val(instructorName);
                $('#editor-classroom-status').val(status);
                
                // Show modal
                $('#vedmg-editor-modal').fadeIn(300);
            });
        },
        
        /**
         * Create management modal
         */
        createManagementModal: function() {
            var modalHtml = `
                <div id="vedmg-management-modal" class="vedmg-modal vedmg-management-modal" style="display: none;">
                    <div class="vedmg-modal-content vedmg-management-modal-content">
                        <div class="vedmg-modal-header">
                            <h3>Manage Course: <span id="management-course-name"></span></h3>
                            <span class="vedmg-modal-close">&times;</span>
                        </div>
                        <div class="vedmg-modal-body vedmg-management-modal-body">
                            <input type="hidden" id="management-course-id" value="">
                            
                            <div class="vedmg-management-grid">
                                <div class="vedmg-management-card">
                                    <h4>📚 Course Information</h4>
                                    <p>View and edit basic course details</p>
                                    <button class="vedmg-classroom-btn vedmg-edit-course-from-modal" data-modal-source="management">Edit Course</button>
                                </div>
                                
                                <div class="vedmg-management-card">
                                    <h4>👥 Student Enrollments</h4>
                                    <p>Manage enrolled students and invitations</p>
                                    <button class="vedmg-classroom-btn vedmg-view-enrollments-btn">View Enrollments</button>
                                </div>
                                
                                <div class="vedmg-management-card">
                                    <h4>📅 Class Sessions</h4>
                                    <p>Schedule and manage class sessions</p>
                                    <button class="vedmg-classroom-btn vedmg-manage-sessions-btn">Manage Sessions</button>
                                </div>
                                
                                <div class="vedmg-management-card">
                                    <h4>🔗 Classroom Link</h4>
                                    <p>Generate or update Google Classroom link</p>
                                    <button class="vedmg-classroom-btn vedmg-meeting-link-from-modal" data-modal-source="management">Generate Link</button>
                                </div>
                                
                                <div class="vedmg-management-card">
                                    <h4>📊 Analytics</h4>
                                    <p>View course performance and statistics</p>
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-view-analytics-btn">View Analytics</button>
                                </div>
                                
                                <div class="vedmg-management-card">
                                    <h4>⚙️ Settings</h4>
                                    <p>Configure course and classroom settings</p>
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-course-settings-btn">Course Settings</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            
            // Bind close events
            $(document).on('click', '#vedmg-management-modal .vedmg-modal-close', function() {
                $('#vedmg-management-modal').fadeOut(300);
            });
            
            // Bind close on outside click
            $(document).on('click', '#vedmg-management-modal', function(e) {
                if (e.target === this) {
                    $('#vedmg-management-modal').fadeOut(300);
                }
            });
            
            // Bind management card actions
            $(document).on('click', '.vedmg-edit-course-from-modal', this.handleEditCourseFromModal);
            $(document).on('click', '.vedmg-view-enrollments-btn', this.handleViewEnrollmentsFromModal);
            $(document).on('click', '.vedmg-manage-sessions-btn', this.handleManageSessionsFromModal);
            $(document).on('click', '.vedmg-meeting-link-from-modal', this.handleMeetingLinkFromModal);
            $(document).on('click', '.vedmg-view-analytics-btn', this.handleViewAnalyticsFromModal);
            $(document).on('click', '.vedmg-course-settings-btn', this.handleCourseSettingsFromModal);
        },
        
        /**
         * Create course viewer modal (read-only)
         */
        createViewerModal: function() {
            var modalHtml = `
                <div id="vedmg-viewer-modal" class="vedmg-modal" style="display: none;">
                    <div class="vedmg-modal-content">
                        <div class="vedmg-modal-header">
                            <h3>Course Details</h3>
                            <span class="vedmg-modal-close">&times;</span>
                        </div>
                        <div class="vedmg-modal-body">
                            <div class="vedmg-course-details">
                                <div class="vedmg-detail-group">
                                    <label>Course Name:</label>
                                    <div id="viewer-course-name" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Course Description:</label>
                                    <div id="viewer-course-description" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Instructor:</label>
                                    <div id="viewer-instructor-name" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Classroom Status:</label>
                                    <div id="viewer-classroom-status" class="vedmg-detail-value vedmg-status-badge"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Google Classroom ID:</label>
                                    <div id="viewer-google-classroom-id" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Student Count:</label>
                                    <div id="viewer-student-count" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Created Date:</label>
                                    <div id="viewer-created-date" class="vedmg-detail-value"></div>
                                </div>
                                
                                <div class="vedmg-detail-group">
                                    <label>Last Updated:</label>
                                    <div id="viewer-updated-date" class="vedmg-detail-value"></div>
                                </div>
                            </div>
                            
                            <div class="vedmg-form-actions">
                                <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-viewer-close">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            
            // Bind close events
            $(document).on('click', '#vedmg-viewer-modal .vedmg-modal-close, .vedmg-viewer-close', function() {
                $('#vedmg-viewer-modal').fadeOut(300);
            });
            
            // Bind close on outside click
            $(document).on('click', '#vedmg-viewer-modal', function(e) {
                if (e.target === this) {
                    $('#vedmg-viewer-modal').fadeOut(300);
                }
            });
        },
        
        /**
         * Create course editor modal
         */
        createEditorModal: function() {
            var modalHtml = `
                <div id="vedmg-editor-modal" class="vedmg-modal" style="display: none;">
                    <div class="vedmg-modal-content">
                        <div class="vedmg-modal-header">
                            <h3>Edit Course</h3>
                            <span class="vedmg-modal-close">&times;</span>
                        </div>
                        <div class="vedmg-modal-body">
                            <form id="vedmg-course-editor-form">
                                <input type="hidden" id="editor-course-id" name="course_id" value="">
                                
                                <div class="vedmg-form-group">
                                    <label for="editor-course-name">Course Name:</label>
                                    <input type="text" id="editor-course-name" name="course_name" class="vedmg-form-control" required>
                                </div>
                                
                                <div class="vedmg-form-group">
                                    <label for="editor-course-description">Course Description:</label>
                                    <textarea id="editor-course-description" name="course_description" class="vedmg-form-control" rows="4"></textarea>
                                </div>
                                
                                <div class="vedmg-form-group">
                                    <label for="editor-instructor-name">Instructor Name:</label>
                                    <input type="text" id="editor-instructor-name" name="instructor_name" class="vedmg-form-control">
                                </div>
                                
                                <div class="vedmg-form-group">
                                    <label for="editor-classroom-status">Classroom Status:</label>
                                    <select id="editor-classroom-status" name="classroom_status" class="vedmg-form-control">
                                        <option value="pending">Pending</option>
                                        <option value="created">Created</option>
                                        <option value="active">Active</option>
                                        <option value="archived">Archived</option>
                                    </select>
                                </div>
                                
                                <div class="vedmg-form-actions">
                                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-editor-cancel">Cancel</button>
                                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary">
                                        <span class="vedmg-classroom-spinner"></span>
                                        Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            
            // Bind close events
            $(document).on('click', '#vedmg-editor-modal .vedmg-modal-close, .vedmg-editor-cancel', function() {
                $('#vedmg-editor-modal').fadeOut(300);
            });
            
            // Bind close on outside click
            $(document).on('click', '#vedmg-editor-modal', function(e) {
                if (e.target === this) {
                    $('#vedmg-editor-modal').fadeOut(300);
                }
            });
            
            // Bind form submission
            $(document).on('submit', '#vedmg-course-editor-form', function(e) {
                e.preventDefault();
                VedMGCourses.saveCourseChanges();
            });
        },
        
        /**
         * Load course data for viewing (read-only)
         */
        loadCourseDataForView: function(courseId) {
            return new Promise(function(resolve, reject) {
                // Check if this is a placeholder course
                if (typeof courseId === 'string' && courseId.indexOf('placeholder') === 0) {
                    reject('Placeholder course data');
                    return;
                }
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_get_course_details',
                        course_id: courseId,
                        nonce: vedmg_classroom_ajax.api_nonce
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'No course data found');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Load course data for editing
         */
        loadCourseData: function(courseId) {
            return new Promise(function(resolve, reject) {
                // Check if this is a placeholder course
                if (typeof courseId === 'string' && courseId.indexOf('placeholder') === 0) {
                    reject('Placeholder course data');
                    return;
                }
                
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'get_course_data',
                        course_id: courseId,
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'No course data found');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Save course changes
         */
        saveCourseChanges: function() {
            var $form = $('#vedmg-course-editor-form');
            var $saveBtn = $form.find('button[type="submit"]');
            var $spinner = $saveBtn.find('.vedmg-classroom-spinner');
            
            // Show loading state
            $saveBtn.prop('disabled', true);
            $spinner.show();
            
            var formData = {
                course_id: $('#editor-course-id').val(),
                course_name: $('#editor-course-name').val(),
                course_description: $('#editor-course-description').val(),
                instructor_name: $('#editor-instructor-name').val(),
                classroom_status: $('#editor-classroom-status').val()
            };
            
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'save_course_data',
                    form_data: formData,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    $saveBtn.prop('disabled', false);
                    $spinner.hide();
                    
                    if (response.success) {
                        VedMGClassRoomAdmin.showMessage('Course updated successfully!', 'success');
                        $('#vedmg-editor-modal').fadeOut(300);
                        
                        // Refresh the course row
                        VedMGCourses.updateCourseRowData(formData.course_id, formData);
                    } else {
                        VedMGClassRoomAdmin.showMessage('Failed to update course: ' + response.data, 'error');
                    }
                },
                error: function() {
                    $saveBtn.prop('disabled', false);
                    $spinner.hide();
                    VedMGClassRoomAdmin.showMessage('Failed to update course. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Update course row data in table
         */
        updateCourseRowData: function(courseId, courseData) {
            var $row = $('tr[data-course-id="' + courseId + '"]');
            if ($row.length) {
                $row.find('td:first strong').text(courseData.course_name);
                $row.find('td:nth-child(2)').text(courseData.instructor_name);
                $row.find('.vedmg-classroom-status').attr('data-status', courseData.classroom_status);
                $row.find('.vedmg-classroom-status').text(courseData.classroom_status.charAt(0).toUpperCase() + courseData.classroom_status.slice(1));
            }
        },
        
        /**
         * Helper functions for management actions
         */
        viewEnrollments: function() {
            var courseId = $('#management-course-id').val();
            window.location.href = 'admin.php?page=vedmg-classroom-enrollments&course_id=' + courseId;
        },
        
        manageSessions: function() {
            var courseId = $('#management-course-id').val();
            window.location.href = 'admin.php?page=vedmg-classroom-sessions&course_id=' + courseId;
        },
        
        /**
         * Modal action handlers
         */
        handleEditCourseFromModal: function(e) {
            e.preventDefault();
            var courseId = $('#management-course-id').val();
            var courseName = $('#management-course-name').text();
            
            // Close management modal first
            $('#vedmg-management-modal').fadeOut(300);
            
            // Open course editor
            setTimeout(function() {
                VedMGCourses.openCourseEditor(courseId, courseName);
            }, 300);
        },
        
        handleViewEnrollmentsFromModal: function(e) {
            e.preventDefault();
            VedMGCourses.viewEnrollments();
        },
        
        handleManageSessionsFromModal: function(e) {
            e.preventDefault();
            VedMGCourses.manageSessions();
        },
        
        handleMeetingLinkFromModal: function(e) {
            e.preventDefault();
            var courseId = $('#management-course-id').val();
            var courseName = $('#management-course-name').text();
            
            // Close management modal first
            $('#vedmg-management-modal').fadeOut(300);
            
            // Open meeting link modal
            setTimeout(function() {
                // Get instructor name (we'll need to derive this from the course data)
                var instructorName = 'Instructor'; // Default fallback
                VedMGCourses.openMeetingModal(courseId, courseName, instructorName);
            }, 300);
        },
        
        handleViewAnalyticsFromModal: function(e) {
            e.preventDefault();
            VedMGClassRoomAdmin.showMessage('Analytics feature coming soon!', 'info');
        },
        
        handleCourseSettingsFromModal: function(e) {
            e.preventDefault();
            VedMGClassRoomAdmin.showMessage('Course settings feature coming soon!', 'info');
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        VedMGCourses.init();
    });
    
    // Make courses object available globally
    window.VedMGCourses = VedMGCourses;
    
})(jQuery);
