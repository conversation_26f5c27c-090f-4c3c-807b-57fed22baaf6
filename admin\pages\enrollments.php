<?php
/**
 * VedMG ClassRoom Student Enrollments Page
 * 
 * This page handles student enrollment management functionality.
 * Allows viewing enrollments and assigning students to Google Classroom classes.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed student enrollments page');

// Handle pagination and filtering parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, intval($_GET['per_page']))) : 10;
$course_filter = isset($_GET['course_id']) ? intval($_GET['course_id']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$classroom_filter = isset($_GET['classroom_id']) ? sanitize_text_field($_GET['classroom_id']) : '';
$search_query = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get paginated data from database
$enrollment_data = VedMG_ClassRoom_Database_Helper::get_student_enrollments($current_page, $per_page, $course_filter, $status_filter, $classroom_filter, $search_query);
$enrollments = $enrollment_data['enrollments'];
$total_count = $enrollment_data['total_count'];
$total_pages = $enrollment_data['total_pages'];

// Get classroom options for dropdowns
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_count);

// Log filtering if active
if ($course_filter || $status_filter || $classroom_filter) {
    vedmg_log_info('ADMIN', 'Filtering enrollments - Course: ' . $course_filter . ', Status: ' . $status_filter . ', Classroom: ' . $classroom_filter);
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Student Enrollments</h1>
        <p>Manage student enrollments and assign them to Google Classroom classes</p>
    </div>
    
    <!-- Filter Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Enrollment Filters</h2>
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn" id="refresh-enrollments">
                    Refresh Enrollments
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="sync-woocommerce">
                    Sync with WooCommerce
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="fetch-classroom-data">
                    Fetch Classroom Data
                </button>
            </div>
        </div>
        
        <form method="GET" class="vedmg-filter-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-filter-group">
                <label for="course_id">Filter by Course:</label>
                <select name="course_id" id="course_id" class="vedmg-filter-select">
                    <option value="">All Courses</option>
                    <?php foreach ($classroom_options as $classroom): ?>
                        <option value="<?php echo $classroom->course_id; ?>" 
                                <?php selected($course_filter, $classroom->course_id); ?>>
                            <?php echo esc_html($classroom->course_name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="classroom_id">Filter by Classroom:</label>
                <select name="classroom_id" id="classroom_id" class="vedmg-filter-select">
                    <option value="">All Classrooms</option>
                    <?php foreach ($classroom_options as $classroom): ?>
                        <?php if (!empty($classroom->google_classroom_id)): ?>
                        <option value="<?php echo esc_attr($classroom->google_classroom_id); ?>" 
                                <?php selected($classroom_filter, $classroom->google_classroom_id); ?>>
                            <?php echo esc_html($classroom->course_name . ' (ID: ' . $classroom->google_classroom_id . ')'); ?>
                        </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <!-- Status filter removed in v2.0 - enrollment_status column no longer exists -->
            
            <div class="vedmg-filter-group">
                <label for="per_page">Items per page:</label>
                <select name="per_page" id="per_page" class="vedmg-filter-select">
                    <option value="10" <?php selected($per_page, 10); ?>>10</option>
                    <option value="25" <?php selected($per_page, 25); ?>>25</option>
                    <option value="50" <?php selected($per_page, 50); ?>>50</option>
                    <option value="100" <?php selected($per_page, 100); ?>>100</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <button type="submit" class="vedmg-classroom-btn">Apply Filters</button>
                <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>" 
                   class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear</a>
            </div>
        </form>
    </div>

    <!-- Search Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Search Enrollments</h2>
            <p>Search by student name, email, instructor name, course name, or classroom ID</p>
        </div>

        <form method="GET" class="vedmg-search-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            <!-- Preserve existing filters -->
            <?php if ($course_filter): ?>
                <input type="hidden" name="course_id" value="<?php echo esc_attr($course_filter); ?>">
            <?php endif; ?>
            <?php if ($status_filter): ?>
                <input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
            <?php endif; ?>
            <?php if ($classroom_filter): ?>
                <input type="hidden" name="classroom_id" value="<?php echo esc_attr($classroom_filter); ?>">
            <?php endif; ?>
            <?php if ($per_page != 10): ?>
                <input type="hidden" name="per_page" value="<?php echo esc_attr($per_page); ?>">
            <?php endif; ?>

            <div class="vedmg-search-group">
                <label for="search">Search:</label>
                <input type="text"
                       name="search"
                       id="search"
                       value="<?php echo esc_attr($search_query); ?>"
                       placeholder="Enter student name, email, instructor, course name, or classroom ID..."
                       class="vedmg-search-input">
            </div>

            <div class="vedmg-search-group">
                <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary">
                    🔍 Search
                </button>
                <?php if ($search_query): ?>
                    <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '') .
                                                  ($course_filter ? '&course_id=' . $course_filter : '') .
                                                  ($status_filter ? '&status=' . $status_filter : '') .
                                                  ($classroom_filter ? '&classroom_id=' . $classroom_filter : '') .
                                                  ($per_page != 10 ? '&per_page=' . $per_page : '')); ?>"
                       class="vedmg-classroom-btn vedmg-classroom-btn-secondary">
                        Clear Search
                    </a>
                <?php endif; ?>
            </div>
        </form>

        <?php if ($search_query): ?>
            <div class="vedmg-search-results-info">
                <p><strong>Search Results for:</strong> "<?php echo esc_html($search_query); ?>"
                   <span class="vedmg-search-count">(<?php echo $total_count; ?> results found)</span></p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Student Enrollment Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Student Enrollments</h2>
            <div class="vedmg-enrollment-summary">
                <span>Total: <strong><?php echo $total_count; ?></strong></span>
                <span>Showing: <strong><?php echo $start_item; ?>-<?php echo $end_item; ?></strong></span>
                <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
            </div>
        </div>
        
        <!-- Bulk Actions -->
        <div class="vedmg-bulk-actions">
            <select id="bulk-action-select">
                <option value="">Bulk Actions</option>
                <option value="enroll">Enroll Selected</option>
                <option value="unenroll">Unenroll Selected</option>
                <option value="delete">Delete Selected</option>
            </select>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="apply-bulk-action">Apply</button>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="group-selected-students" style="display: none;">
                Group Selected Students
            </button>
        </div>
        
        <!-- Student Enrollment Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-enrollments"></th>
                    <th>Student Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Course</th>
                    <th>Instructor Name</th>
                    <th>Google Classroom</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($enrollments)): ?>
                    <?php foreach ($enrollments as $enrollment): ?>
                        <tr class="enrollment-row" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>" data-student-id="<?php echo $enrollment->student_id; ?>" data-course-id="<?php echo $enrollment->course_id; ?>">
                            <td><input type="checkbox" class="enrollment-checkbox" value="<?php echo $enrollment->enrollment_id; ?>" data-student-id="<?php echo $enrollment->student_id; ?>" data-course-id="<?php echo $enrollment->course_id; ?>"></td>
                            <td>
                                <strong><?php echo esc_html($enrollment->student_name ?: 'Unknown Student'); ?></strong>
                                <?php if (isset($enrollment->user_status) && $enrollment->user_status === 'missing_user'): ?>
                                    <small style="display: block; color: #d63638; font-style: italic;">(User account missing)</small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($enrollment->student_email ?: 'No email'); ?></td>
                            <td><?php echo esc_html($enrollment->student_phone ?: 'No phone'); ?></td>
                            <td><?php echo esc_html($enrollment->course_name ?: 'Unknown Course'); ?></td>
                            <td>
                                <strong><?php echo esc_html($enrollment->instructor_name ?: 'Unknown Instructor'); ?></strong>
                                <?php if (!empty($enrollment->last_scheduled_date)): ?>
                                    <small style="display: block; color: #666; font-style: italic;">
                                        Last scheduled: <?php echo date('M j, Y', strtotime($enrollment->last_scheduled_date)); ?>
                                        <?php if ($enrollment->total_sessions_scheduled > 0): ?>
                                            (<?php echo $enrollment->total_sessions_scheduled; ?> sessions)
                                        <?php endif; ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($enrollment->google_classroom_id)): ?>
                                    <?php
                                    // Use the course name from the enrollment data (already joined from courses table)
                                    $classroom_name = !empty($enrollment->course_name) ? $enrollment->course_name : 'Unknown Course';
                                    ?>
                                    <div class="vedmg-classroom-assigned">
                                        <span class="vedmg-classroom-name"><?php echo esc_html($classroom_name); ?></span>
                                        <small class="vedmg-classroom-id">ID: <?php echo esc_html($enrollment->google_classroom_id); ?></small>
                                    </div>
                                <?php else: ?>
                                    <select class="classroom-select" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>">
                                        <option value="">Select Classroom</option>
                                        <?php foreach ($classroom_options as $classroom): ?>
                                            <option value="<?php echo $classroom->course_id; ?>">
                                                <?php echo esc_html($classroom->course_name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <?php
                                    // Enhanced Schedule Lab button with state management
                                    $has_sessions = !empty($enrollment->total_sessions_scheduled) && $enrollment->total_sessions_scheduled > 0;
                                    $schedule_btn_class = $has_sessions
                                        ? 'vedmg-classroom-btn vedmg-classroom-btn-success vedmg-schedule-lab-btn scheduled'
                                        : 'vedmg-classroom-btn vedmg-classroom-btn-accent vedmg-schedule-lab-btn';
                                    $schedule_btn_text = $has_sessions ? 'Reschedule Lab' : 'Schedule Lab';
                                    $schedule_btn_icon = $has_sessions ? '🔄' : '📅';
                                    ?>
                                    <button class="<?php echo $schedule_btn_class; ?>"
                                            data-student-id="<?php echo $enrollment->student_id; ?>"
                                            data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>"
                                            data-course-id="<?php echo $enrollment->course_id; ?>"
                                            data-has-sessions="<?php echo $has_sessions ? '1' : '0'; ?>"
                                            title="<?php echo $has_sessions ? 'Student has ' . $enrollment->total_sessions_scheduled . ' scheduled sessions' : 'No sessions scheduled yet'; ?>">
                                        <span class="vedmg-btn-icon"><?php echo $schedule_btn_icon; ?></span>
                                        <span class="vedmg-btn-text"><?php echo $schedule_btn_text; ?></span>
                                        <?php if ($has_sessions): ?>
                                            <span class="vedmg-session-count">(<?php echo $enrollment->total_sessions_scheduled; ?>)</span>
                                        <?php endif; ?>
                                    </button>

                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-view-student-btn"
                                            data-student-id="<?php echo $enrollment->student_id; ?>"
                                            title="View student details and session history">
                                        <span class="vedmg-btn-icon">👁️</span>
                                        <span class="vedmg-btn-text">View Details</span>
                                    </button>

                                    <!-- Classroom Management Buttons -->
                                    <?php if (isset($enrollment->classroom_status)): ?>
                                        <?php if ($enrollment->classroom_status === 'pending'): ?>
                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-primary vedmg-create-classroom-btn"
                                                    data-course-id="<?php echo $enrollment->course_id; ?>"
                                                    data-course-name="<?php echo esc_attr($enrollment->course_name); ?>"
                                                    data-instructor-email="<?php echo esc_attr($enrollment->instructor_email ?? ''); ?>"
                                                    data-instructor-name="<?php echo esc_attr($enrollment->instructor_name ?? ''); ?>">
                                                <span class="vedmg-classroom-spinner"></span>
                                                Create Classroom
                                            </button>
                                        <?php elseif ($enrollment->classroom_status === 'active'): ?>
                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-warning vedmg-archive-classroom-btn"
                                                    data-course-id="<?php echo $enrollment->course_id; ?>"
                                                    data-classroom-id="<?php echo esc_attr($enrollment->google_classroom_id); ?>">
                                                <span class="vedmg-classroom-spinner"></span>
                                                Archive
                                            </button>

                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-delete-classroom-btn"
                                                    data-course-id="<?php echo $enrollment->course_id; ?>"
                                                    data-classroom-id="<?php echo esc_attr($enrollment->google_classroom_id); ?>">
                                                <span class="vedmg-classroom-spinner"></span>
                                                Delete
                                            </button>
                                        <?php elseif ($enrollment->classroom_status === 'archived'): ?>
                                            <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-delete-classroom-btn"
                                                    data-course-id="<?php echo $enrollment->course_id; ?>"
                                                    data-classroom-id="<?php echo esc_attr($enrollment->google_classroom_id); ?>">
                                                <span class="vedmg-classroom-spinner"></span>
                                                Delete
                                            </button>

                                            <span class="vedmg-status-badge vedmg-status-archived">Archived</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 20px; color: #666;">
                            <?php if ($course_filter || $status_filter): ?>
                                <em>No enrollments found matching the current filters.</em>
                                <br><a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>">Clear filters</a>
                            <?php else: ?>
                                <em>No student enrollments found in database. Students will appear here after purchasing courses.</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Server-Side Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination">
        <div class="vedmg-pagination-info">
            Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_count; ?> enrollments
        </div>
        <div class="vedmg-pagination-controls">
            <?php
            $base_url = admin_url('admin.php');
            $query_args = array_merge($_GET, array('page' => $_GET['page']));
            
            // First page
            if ($current_page > 1):
                $first_url = add_query_arg(array_merge($query_args, array('paged' => 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($first_url); ?>" class="vedmg-pagination-btn">‹‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹‹</span>
            <?php endif; ?>
            
            <?php
            // Previous page
            if ($current_page > 1):
                $prev_url = add_query_arg(array_merge($query_args, array('paged' => $current_page - 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($prev_url); ?>" class="vedmg-pagination-btn">‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹</span>
            <?php endif; ?>
            
            <div class="vedmg-pagination-numbers">
                <?php
                // Calculate page range
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++):
                    if ($i === $current_page):
                ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else:
                    $page_url = add_query_arg(array_merge($query_args, array('paged' => $i)), $base_url);
                ?>
                    <a href="<?php echo esc_url($page_url); ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif; endfor; ?>
            </div>
            
            <?php
            // Next page
            if ($current_page < $total_pages):
                $next_url = add_query_arg(array_merge($query_args, array('paged' => $current_page + 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($next_url); ?>" class="vedmg-pagination-btn">›</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">›</span>
            <?php endif; ?>
            
            <?php
            // Last page
            if ($current_page < $total_pages):
                $last_url = add_query_arg(array_merge($query_args, array('paged' => $total_pages)), $base_url);
            ?>
                <a href="<?php echo esc_url($last_url); ?>" class="vedmg-pagination-btn">››</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">››</span>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Enrollment Edit Modal -->
<div id="vedmg-enrollment-edit-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Edit Student Enrollment</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-enrollment-edit-form">
                <input type="hidden" id="edit-enrollment-id" name="enrollment_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-name">Student Name:</label>
                        <input type="text" id="edit-student-name" name="student_name" class="vedmg-form-control" readonly>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-email">Student Email:</label>
                        <input type="email" id="edit-student-email" name="student_email" class="vedmg-form-control" readonly>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-course">Course:</label>
                        <select id="edit-enrollment-course" name="course_id" class="vedmg-form-control" required>
                            <option value="">Select Course</option>
                            <?php foreach ($classroom_options as $classroom): ?>
                                <option value="<?php echo $classroom->course_id; ?>">
                                    <?php echo esc_html($classroom->course_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-status">Enrollment Status:</label>
                        <select id="edit-enrollment-status" name="enrollment_status" class="vedmg-form-control">
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="dropped">Dropped</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-date">Enrollment Date:</label>
                        <input type="date" id="edit-enrollment-date" name="enrollment_date" class="vedmg-form-control" required>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-classroom-status">Classroom Status:</label>
                        <select id="edit-classroom-status" name="classroom_status" class="vedmg-form-control">
                            <option value="not_invited">Not Invited</option>
                            <option value="invited">Invited</option>
                            <option value="joined">Joined</option>
                            <option value="removed">Removed</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="edit-enrollment-notes">Notes:</label>
                    <textarea id="edit-enrollment-notes" name="notes" class="vedmg-form-control" rows="3" placeholder="Additional notes about this enrollment..."></textarea>
                </div>
                
                <div class="vedmg-form-group">
                    <label>Actions:</label>
                    <div class="vedmg-checkbox-group">
                        <label>
                            <input type="checkbox" id="edit-send-classroom-invite" name="send_classroom_invite" value="1">
                            Send Google Classroom invitation
                        </label>
                        <label>
                            <input type="checkbox" id="edit-send-welcome-email" name="send_welcome_email" value="1">
                            Send welcome email to student
                        </label>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-enrollment-edit">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-enrollment-edit">
                        <span class="vedmg-classroom-spinner"></span>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Student Details Modal -->
<div id="vedmg-student-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Student Details</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div class="vedmg-student-details">
                <div class="vedmg-student-profile">
                    <div class="vedmg-profile-avatar">
                        <div class="vedmg-avatar-placeholder"></div>
                    </div>
                    <div class="vedmg-profile-info">
                        <h4 id="details-student-name">--</h4>
                        <p id="details-student-email">--</p>
                        <span class="vedmg-enrollment-status" id="details-student-status">--</span>
                    </div>
                </div>
                
                <div class="vedmg-details-grid">
                    <div class="vedmg-detail-item">
                        <label>Student ID:</label>
                        <span id="details-student-id">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Total Enrollments:</label>
                        <span id="details-student-enrollments">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Active Courses:</label>
                        <span id="details-student-active-courses">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Join Date:</label>
                        <span id="details-student-join-date">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Last Activity:</label>
                        <span id="details-student-last-activity">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Completion Rate:</label>
                        <span id="details-student-completion-rate">--</span>
                    </div>
                </div>
                
                <div class="vedmg-student-enrollments-list">
                    <h4>Current Enrollments</h4>
                    <div id="details-student-enrollment-list">
                        <p>Loading enrollments...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vedmg-modal-footer">
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Send Message</button>
        </div>
    </div>
</div>

<!-- Enrollment Edit Modal -->
<div id="vedmg-enrollment-edit-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Edit Student Enrollment</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-enrollment-edit-form">
                <input type="hidden" id="edit-enrollment-id" name="enrollment_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-name">Student Name:</label>
                        <input type="text" id="edit-student-name" name="student_name" class="vedmg-form-control" readonly>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-email">Student Email:</label>
                        <input type="email" id="edit-student-email" name="student_email" class="vedmg-form-control" readonly>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-course">Course:</label>
                        <select id="edit-enrollment-course" name="course_id" class="vedmg-form-control" required>
                            <option value="">Select Course</option>
                            <?php foreach ($classroom_options as $classroom): ?>
                                <option value="<?php echo $classroom->course_id; ?>">
                                    <?php echo esc_html($classroom->course_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-status">Enrollment Status:</label>
                        <select id="edit-enrollment-status" name="enrollment_status" class="vedmg-form-control">
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="dropped">Dropped</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-enrollment-edit">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-enrollment-edit">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Schedule Lab Modal -->
<div id="vedmg-schedule-lab-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Schedule Lab Session</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-schedule-lab-form">
                <!-- Hidden fields for API data -->
                <input type="hidden" id="lab-student-id" name="student_id" value="">
                <input type="hidden" id="lab-enrollment-id" name="enrollment_id" value="">
                <input type="hidden" id="lab-course-id" name="course_id" value="">
                <input type="hidden" id="lab-calendar-id" name="calendar_id" value="">
                <input type="hidden" id="lab-instructor-email" name="instructor_email" value="">
                <input type="hidden" id="lab-student-email" name="student_email" value="">

                <!-- Student Info Display -->
                <div class="vedmg-form-group">
                    <div class="vedmg-student-info">
                        <strong>Student:</strong> <span id="lab-student-name">-</span>
                        (<span id="lab-student-email-display">-</span>)
                    </div>
                </div>

                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-summary">Session Title (Summary):</label>
                        <input type="text" id="lab-session-summary" name="summary" class="vedmg-form-control"
                               placeholder="e.g., Live Presentation Session" required>
                        <small class="vedmg-form-note">This will appear as the calendar event title</small>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-type">Session Type:</label>
                        <select id="lab-session-type" name="session_type" class="vedmg-form-control" required>
                            <option value="">Select Type</option>
                            <option value="individual">Individual Session</option>
                            <option value="group">Group Session</option>
                            <option value="class">Class-wide Session</option>
                        </select>
                    </div>
                </div>
                
                <!-- Date and Time Section -->
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-date">Start Date:</label>
                        <input type="date" id="lab-session-date" name="session_date" class="vedmg-form-control" required>
                        <small class="vedmg-form-note">Session start date (YYYY-MM-DD)</small>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-start-time">Start Time:</label>
                        <input type="time" id="lab-start-time" name="start_time" class="vedmg-form-control" required>
                        <small class="vedmg-form-note">24-hour format (HH:MM)</small>
                    </div>
                </div>

                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-end-date">End Date:</label>
                        <input type="date" id="lab-end-date" name="end_date" class="vedmg-form-control" required>
                        <small class="vedmg-form-note">Session end date (YYYY-MM-DD)</small>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-end-time">End Time:</label>
                        <input type="time" id="lab-end-time" name="end_time" class="vedmg-form-control" required>
                        <small class="vedmg-form-note">24-hour format (HH:MM)</small>
                    </div>
                </div>

                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-timezone">Timezone:</label>
                        <select id="lab-timezone" name="timezone" class="vedmg-form-control" required>
                            <option value="+05:30" selected>India Standard Time (IST) +05:30</option>
                            <option value="+00:00">UTC +00:00</option>
                            <option value="+01:00">Central European Time +01:00</option>
                            <option value="-05:00">Eastern Time (US) -05:00</option>
                            <option value="-08:00">Pacific Time (US) -08:00</option>
                        </select>
                        <small class="vedmg-form-note">Select appropriate timezone</small>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label>
                            <input type="checkbox" id="lab-same-day" name="same_day" checked>
                            Same day session
                        </label>
                        <small class="vedmg-form-note">Uncheck if session spans multiple days</small>
                    </div>
                </div>
                
                <!-- Description Section -->
                <div class="vedmg-form-group">
                    <label for="lab-description">Session Description:</label>
                    <textarea id="lab-description" name="description" class="vedmg-form-control" rows="4"
                              placeholder="Enter session description and objectives. The meeting link will be automatically added."></textarea>
                    <small class="vedmg-form-note">
                        <strong>Note:</strong> The meeting link from the course will be automatically appended to this description.
                        <br>Example: "Your description here. Please join at https://meet.google.com/xxx-xxxx-xxx"
                    </small>
                </div>

                <!-- Meeting Link Display -->
                <div class="vedmg-form-group">
                    <label>Meeting Link (Auto-filled from Course):</label>
                    <div class="vedmg-meeting-link-display">
                        <input type="text" id="lab-meeting-link-display" class="vedmg-form-control" readonly
                               placeholder="Meeting link will be loaded from course data">
                        <small class="vedmg-form-note">This link will be automatically included in the calendar invite</small>
                    </div>
                </div>

                <!-- Location (Fixed for Google Meet) -->
                <input type="hidden" id="lab-location" name="location" value="On Google Meet">
                
                <!-- Recurring Session Options -->
                <div class="vedmg-form-group">
                    <label>
                        <input type="checkbox" id="lab-recurring" name="is_recurring" value="1">
                        Make this a recurring session
                    </label>
                    <small class="vedmg-form-note">Creates multiple calendar events based on the pattern below</small>
                </div>

                <div id="lab-recurring-options" style="display: none;">
                    <div class="vedmg-form-row">
                        <div class="vedmg-form-group vedmg-form-half">
                            <label for="lab-recurring-frequency">Frequency:</label>
                            <select id="lab-recurring-frequency" name="recurring_frequency" class="vedmg-form-control">
                                <option value="WEEKLY">Weekly</option>
                                <option value="DAILY">Daily</option>
                                <option value="MONTHLY">Monthly</option>
                            </select>
                            <small class="vedmg-form-note">How often the session repeats</small>
                        </div>
                        <div class="vedmg-form-group vedmg-form-half">
                            <label for="lab-recurring-count">Number of Occurrences:</label>
                            <input type="number" id="lab-recurring-count" name="recurring_count"
                                   class="vedmg-form-control" min="1" max="52" value="3" required>
                            <small class="vedmg-form-note">Total number of sessions to create</small>
                        </div>
                    </div>
                    
                    <!-- Days Selection for Weekly Recurring -->
                    <div class="vedmg-form-group" id="weekly-days-section" style="display: none;">
                        <label>Days of the Week (for weekly frequency):</label>
                        <div class="vedmg-checkbox-group">
                            <label><input type="checkbox" name="recurring_days[]" value="MO"> Monday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="TU"> Tuesday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="WE"> Wednesday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="TH"> Thursday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="FR"> Friday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="SA"> Saturday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="SU"> Sunday</label>
                        </div>
                        <small class="vedmg-form-note">
                            Select which days of the week the session should repeat.
                            <br><strong>Example:</strong> Selecting Mon, Wed, Fri will create pattern "BYDAY=MO,WE,FR"
                        </small>
                    </div>

                    <!-- Month and Dates Selection for Monthly Recurring -->
                    <div class="vedmg-form-group" id="monthly-selection-section" style="display: none;">
                        <div class="vedmg-form-row">
                            <div class="vedmg-form-group vedmg-form-half">
                                <label for="lab-recurring-month">Select Month:</label>
                                <select id="lab-recurring-month" name="recurring_month" class="vedmg-form-control">
                                    <option value="">Select Month</option>
                                    <option value="01">January</option>
                                    <option value="02">February</option>
                                    <option value="03">March</option>
                                    <option value="04">April</option>
                                    <option value="05">May</option>
                                    <option value="06">June</option>
                                    <option value="07">July</option>
                                    <option value="08">August</option>
                                    <option value="09">September</option>
                                    <option value="10">October</option>
                                    <option value="11">November</option>
                                    <option value="12">December</option>
                                </select>
                                <small class="vedmg-form-note">Select the month for recurring sessions</small>
                            </div>
                            <div class="vedmg-form-group vedmg-form-half">
                                <label>Sessions Limit:</label>
                                <div class="vedmg-sessions-limit-display">
                                    <span id="selected-dates-count">0</span> / <span id="max-sessions-count">3</span> sessions selected
                                </div>
                                <small class="vedmg-form-note">You can select up to <span id="max-sessions-text">3</span> dates based on the number of occurrences</small>
                            </div>
                        </div>

                        <div class="vedmg-form-group" id="monthly-dates-section" style="display: none;">
                            <label>Select Dates (for monthly frequency):</label>
                            <div class="vedmg-checkbox-group vedmg-monthly-dates">
                                <?php for($i = 1; $i <= 31; $i++): ?>
                                    <label class="vedmg-date-option">
                                        <input type="checkbox" name="recurring_dates[]" value="<?php echo sprintf('%02d', $i); ?>">
                                        <?php echo $i; ?>
                                    </label>
                                <?php endfor; ?>
                            </div>
                            <small class="vedmg-form-note">
                                Select specific dates of the month. Invalid dates (like 31st for February) will be automatically skipped.
                                <br><strong>Note:</strong> You can only select up to the number of sessions specified above.
                            </small>
                        </div>
                    </div>

                    <!-- Preview of Meeting Frequency -->
                    <div class="vedmg-form-group">
                        <label>Meeting Frequency Preview:</label>
                        <div class="vedmg-frequency-preview">
                            <code id="lab-frequency-preview">FREQ=WEEKLY;BYDAY=MO,TU,WE;COUNT=3</code>
                        </div>
                        <small class="vedmg-form-note">This is the pattern that will be sent to Google Calendar API</small>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-schedule-lab">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-schedule-lab">
                        <span class="vedmg-classroom-spinner"></span>
                        <span id="save-schedule-lab-text">Create Calendar Event</span>
                    </button>
                </div>

                <!-- Real-time API Body Preview -->
                <div class="vedmg-form-group" style="margin-top: 20px;">
                    <div class="vedmg-api-preview-section">
                        <h4 style="margin-bottom: 10px; color: #333;">📡 Request Body Preview</h4>
                        <div>
                            <pre id="api-body-preview" style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 6px; font-size: 13px; overflow-x: auto; max-height: 300px; font-family: 'Courier New', monospace;">
{
  "calendar_id": "Loading...",
  "user_email": "Loading...",
  "summary": "Enter session title above",
  "location": "On Google Meet",
  "description": "Enter description above. Meeting link will be appended.",
  "start_datetime": "Select date and time above",
  "end_datetime": "Select date and time above",
  "instructor_email": "Loading...",
  "meeting_frequency": "Configure recurrence above"
}
                            </pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <small style="color: #6c757d; font-style: italic;">
                                ℹ️ This is the exact request body that will be sent to the API for each student.
                            </small>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Session Details Modal -->
<div id="vedmg-session-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Session Details</h3>
            <span class="vedmg-modal-close" id="close-session-details">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div id="session-details-content">
                <!-- Session details will be loaded here via AJAX -->
                <div class="vedmg-loading">Loading session details...</div>
            </div>
        </div>
        <div class="vedmg-modal-footer">
            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="close-session-details-btn">Close</button>
        </div>
    </div>
</div>

<style>
/* Modal Styles - Same as courses.php for consistency */
.vedmg-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    border-radius: 8px 8px 0 0;
}

.vedmg-modal-header h3 {
    margin: 0;
    color: #333;
}

.vedmg-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    padding: 5px;
    line-height: 1;
    border: none;
    background: none;
    outline: none;
}

.vedmg-modal-close:hover {
    color: #000;
}

.vedmg-modal-body {
    padding: 25px;
}

.vedmg-modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #ddd;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group {
    margin-bottom: 20px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.vedmg-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.vedmg-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-form-row {
    display: flex;
    gap: 15px;
}

.vedmg-form-half {
    flex: 1;
}

.vedmg-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Student Details Specific Styles */
.vedmg-student-details {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.vedmg-student-profile {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.vedmg-profile-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vedmg-avatar-placeholder {
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.vedmg-profile-info h4 {
    margin: 0 0 5px 0;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-profile-info p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
}

.vedmg-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.vedmg-detail-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.vedmg-detail-item label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 5px;
}

.vedmg-detail-item span {
    font-size: 16px;
    font-weight: 500;
    color: #23282d;
}

.vedmg-student-enrollments-list h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-enrollment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
}

.vedmg-enrollment-item .course-name {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
}

.vedmg-enrollment-item .enrollment-date {
    font-size: 12px;
    color: #666;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .vedmg-modal-header,
    .vedmg-modal-body,
    .vedmg-modal-footer {
        padding: 15px;
    }
    
    .vedmg-student-profile {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .vedmg-details-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-modal-footer {
        flex-direction: column;
    }
    
    .vedmg-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .vedmg-form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .vedmg-modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .vedmg-modal-header h3 {
        font-size: 16px;
    }
}

/* Schedule Lab Modal Specific Styles */
.vedmg-classroom-btn-accent {
    background-color: #8E44AD;
    color: white;
    border: 1px solid #8E44AD;
}

/* Student Info Display */
.vedmg-student-info {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
}

/* Meeting Link Display */
.vedmg-meeting-link-display input {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}

/* Form Notes */
.vedmg-form-note {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

/* Frequency Preview */
.vedmg-frequency-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
}

.vedmg-frequency-preview code {
    background: none;
    color: #495057;
    font-weight: bold;
}

/* Checkbox Groups */
.vedmg-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 8px;
}

.vedmg-checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin: 0;
    cursor: pointer;
}

.vedmg-checkbox-group input[type="checkbox"] {
    margin-right: 6px;
}

/* Monthly Selection Styles */
.vedmg-monthly-dates {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    padding: 10px;
    border-radius: 4px;
    background: #f8f9fa;
}

.vedmg-date-option {
    min-width: 40px;
    text-align: center;
    padding: 5px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    background: white;
    margin: 2px;
    transition: all 0.2s;
}

.vedmg-date-option:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.vedmg-date-option input[type="checkbox"]:checked + span,
.vedmg-date-option:has(input[type="checkbox"]:checked) {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.vedmg-date-option.disabled {
    opacity: 0.5;
    pointer-events: none;
    background: #f8f9fa;
}

.vedmg-sessions-limit-display {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    padding: 8px;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

/* API Preview Styles */
.vedmg-api-preview-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

#api-body-preview {
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.4;
}

.vedmg-api-preview-section h4 {
    margin-top: 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.vedmg-api-preview-section code {
    font-size: 12px;
}

/* Session Details Modal Specific Styles */
.vedmg-session-summary {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    text-align: center;
}

.vedmg-session-detail-card {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.vedmg-session-header {
    background: #007cba;
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vedmg-session-header h4 {
    margin: 0;
    font-size: 16px;
}

.vedmg-session-type {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.vedmg-session-info {
    padding: 15px;
}

.vedmg-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.vedmg-info-item {
    padding: 8px;
    background: #f8f9fa;
    border-left: 3px solid #007cba;
    border-radius: 0 3px 3px 0;
}

.vedmg-info-item strong {
    color: #495057;
    display: block;
    margin-bottom: 4px;
}

.vedmg-session-description {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.vedmg-enrolled-students {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    padding: 12px;
    border-radius: 4px;
}

.vedmg-enrolled-students ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.vedmg-enrolled-students li {
    margin-bottom: 5px;
    color: #495057;
}

.vedmg-loading, .vedmg-error, .vedmg-info {
    text-align: center;
    padding: 40px 20px;
    font-size: 16px;
}

.vedmg-loading {
    color: #6c757d;
}

.vedmg-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

.vedmg-info {
    color: #0c5460;
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
}

/* Classroom Display Styles */
.vedmg-classroom-assigned {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
    padding: 8px 10px;
    display: inline-block;
    min-width: 150px;
}

.vedmg-classroom-name {
    font-weight: 600;
    color: #2e7d32;
    display: block;
    margin-bottom: 2px;
}

.vedmg-classroom-id {
    color: #666;
    font-size: 11px;
    display: block;
}

.classroom-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

/* Group Selected Students Button */
#group-selected-students {
    margin-left: 10px;
    background-color: #0073aa;
    border-color: #005a87;
}

#group-selected-students:hover {
    background-color: #005a87;
    border-color: #004b72;
}

.vedmg-bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.vedmg-classroom-btn-accent:hover {
    background-color: #9B59B6;
    border-color: #9B59B6;
}

.vedmg-classroom-btn-primary {
    background-color: #4A90E2;
    color: white;
    border: 1px solid #4A90E2;
}

.vedmg-classroom-btn-primary:hover {
    background-color: #357ABD;
    border-color: #357ABD;
}

.vedmg-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.vedmg-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
    margin: 0;
}

.vedmg-checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* Monthly dates specific styling */
.vedmg-monthly-dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    max-width: 400px;
}

.vedmg-monthly-dates label {
    justify-content: center;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    transition: all 0.2s ease;
}

.vedmg-monthly-dates label:hover {
    background: #e9e9e9;
    border-color: #ccc;
}

.vedmg-monthly-dates input[type="checkbox"]:checked + span,
.vedmg-monthly-dates label:has(input[type="checkbox"]:checked) {
    background: #007cba;
    color: white;
    border-color: #005a87;
}

.vedmg-form-note {
    display: block;
    margin-top: 8px;
    color: #666;
    font-style: italic;
    font-size: 12px;
}

/* Scheduled button state */
.vedmg-scheduled-btn {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.vedmg-scheduled-btn:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}

/* Inactive scheduled button state (no sessions) */
.vedmg-scheduled-btn-inactive {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.vedmg-scheduled-btn-inactive:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Required field styling */
.vedmg-required {
    color: #dc3545;
    font-weight: bold;
}

.vedmg-form-control:required {
    border-left: 3px solid #007cba;
}

.vedmg-form-control:required:invalid {
    border-left-color: #dc3545;
}

.vedmg-form-control:required:valid {
    border-left-color: #28a745;
}

#lab-recurring-options {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
    background-color: #f9f9f9;
}

#lab-recurring-options .vedmg-form-row {
    margin-bottom: 15px;
}

#lab-recurring-options .vedmg-form-row:last-child {
    margin-bottom: 0;
}

.vedmg-section-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .vedmg-checkbox-group {
        flex-direction: column;
        gap: 5px;
    }
    
    .vedmg-section-actions {
        flex-direction: column;
    }
    
    .vedmg-section-actions button {
        width: 100%;
    }
}

/* Classroom Management Styles */
.vedmg-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 5px;
}

.vedmg-status-archived {
    background-color: #f39c12;
    color: white;
}

.vedmg-classroom-btn-primary {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

.vedmg-classroom-btn-primary:hover {
    background-color: #2980b9;
}

.vedmg-classroom-btn-success {
    background-color: #27ae60;
    color: white;
    border: 1px solid #229954;
}

.vedmg-classroom-btn-success:hover {
    background-color: #229954;
}

.vedmg-classroom-btn-warning {
    background-color: #f39c12;
    color: white;
    border: 1px solid #e67e22;
}

.vedmg-classroom-btn-warning:hover {
    background-color: #e67e22;
}

.vedmg-classroom-btn-danger {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

.vedmg-classroom-btn-danger:hover {
    background-color: #c0392b;
}

.vedmg-classroom-spinner {
    display: none;
    width: 12px;
    height: 12px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

.vedmg-classroom-spinner.spinning {
    display: inline-block;
}

/* Loading Overlay Styles */
.vedmg-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: none;
}

.vedmg-loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 300px;
}

.vedmg-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: vedmg-spin 1s linear infinite;
    margin: 0 auto 20px;
}

.vedmg-loading-message {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-top: 10px;
}

@keyframes vedmg-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.vedmg-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
}

.vedmg-action-buttons .vedmg-classroom-btn {
    margin: 2px;
    padding: 6px 12px;
    font-size: 12px;
    white-space: nowrap;
}

/* Custom Popup Styles */
.vedmg-custom-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.vedmg-custom-popup {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    margin: auto;
    position: relative;
    animation: vedmg-popup-appear 0.3s ease-out;
}

@keyframes vedmg-popup-appear {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.vedmg-popup-header {
    background: #f8f9fa;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
}

.vedmg-popup-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.vedmg-popup-body {
    padding: 24px;
}

.vedmg-popup-body p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    color: #555;
}

.vedmg-popup-footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.vedmg-popup-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.vedmg-popup-btn-primary {
    background: #007cba;
    color: white;
}

.vedmg-popup-btn-primary:hover {
    background: #005a87;
}

.vedmg-popup-btn-confirm {
    background: #dc3545;
    color: white;
}

.vedmg-popup-btn-confirm:hover {
    background: #c82333;
}

.vedmg-popup-btn-cancel {
    background: #6c757d;
    color: white;
}

.vedmg-popup-btn-cancel:hover {
    background: #5a6268;
}

.vedmg-popup-btn:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .vedmg-custom-popup {
        max-width: 95%;
        margin: 10px;
    }

    .vedmg-popup-header,
    .vedmg-popup-body,
    .vedmg-popup-footer {
        padding: 16px;
    }

    .vedmg-popup-footer {
        flex-direction: column;
        gap: 8px;
    }

    .vedmg-popup-btn {
        width: 100%;
    }
}

/* Ensure popup is always visible */
.vedmg-custom-popup-overlay.show {
    display: flex !important;
}

</style>

<script>
// Enrollment management specific JavaScript for server-side pagination
jQuery(document).ready(function($) {
    console.log('Student enrollments page loaded with server-side pagination');
    
    // Simple refresh functionality
    $('#refresh-enrollments').on('click', function(e) {
        e.preventDefault();
        $(this).prop('disabled', true).text('Refreshing...');
        setTimeout(function() {
            window.location.reload();
        }, 500);
    });
    
    // Handle checkbox selection for group button visibility
    function updateGroupButtonVisibility() {
        var checkedCount = $('.enrollment-checkbox:checked').length;
        if (checkedCount > 1) {
            $('#group-selected-students').show();
        } else {
            $('#group-selected-students').hide();
        }
    }
    
    // Bind checkbox events
    $('#select-all-enrollments').on('change', function() {
        $('.enrollment-checkbox').prop('checked', $(this).is(':checked'));
        updateGroupButtonVisibility();
    });
    
    $('.enrollment-checkbox').on('change', function() {
        var totalCheckboxes = $('.enrollment-checkbox').length;
        var checkedCheckboxes = $('.enrollment-checkbox:checked').length;
        $('#select-all-enrollments').prop('checked', checkedCheckboxes === totalCheckboxes);
        updateGroupButtonVisibility();
    });
    
    // Handle group selected students button click
    $('#group-selected-students').on('click', function() {
        var selectedStudents = $('.enrollment-checkbox:checked');
        if (selectedStudents.length > 1) {
            // Open schedule lab modal with group session pre-selected
            openGroupScheduleModal(selectedStudents);
        }
    });
    
    function openGroupScheduleModal(selectedStudents) {
        // Set session type to group and disable it
        $('#lab-session-type').val('group').prop('disabled', true);

        // Get course ID from the first selected checkbox (all should be from same course)
        var firstCheckbox = selectedStudents.first();
        var courseId = firstCheckbox.data('course-id');
        var studentId = firstCheckbox.data('student-id');

        console.log('Group session data:', {
            selectedCount: selectedStudents.length,
            courseId: courseId,
            studentId: studentId
        });

        if (!courseId) {
            alert('Error: Could not determine course ID for group session');
            return;
        }

        // Set basic form data
        $('#lab-course-id').val(courseId);

        // Fetch course data and populate modal, then show it
        fetchCourseDataAndPopulateModal(courseId, studentId);

        console.log('Group schedule modal opened for', selectedStudents.length, 'students', 'Course ID:', courseId);
    }
    
    // Handle individual schedule lab button (normal behavior)
    $('.vedmg-schedule-lab-btn').on('click', function() {
        var $button = $(this);
        var studentId = $button.data('student-id');
        var enrollmentId = $button.data('enrollment-id');
        var courseId = $button.data('course-id');

        console.log('Schedule Lab button clicked:', {
            studentId: studentId,
            enrollmentId: enrollmentId,
            courseId: courseId
        });

        // Reset session type to allow individual/class selection only (hide group)
        var $sessionType = $('#lab-session-type');
        $sessionType.prop('disabled', false);
        $sessionType.empty();
        $sessionType.append('<option value="">Select Type</option>');
        $sessionType.append('<option value="individual">Individual Session</option>');
        $sessionType.append('<option value="class">Class-wide Session</option>');
        // Note: Group session is intentionally excluded for regular schedule lab button

        // Set basic form data
        $('#lab-student-id').val(studentId);
        $('#lab-enrollment-id').val(enrollmentId);
        $('#lab-course-id').val(courseId);

        // Fetch course data and populate modal
        fetchCourseDataAndPopulateModal(courseId, studentId);

        console.log('Individual schedule modal opened');
    });

    /**
     * Fetch course data and populate modal fields
     */
    function fetchCourseDataAndPopulateModal(courseId, studentId) {
        console.log('Fetching course data for course:', courseId, 'student:', studentId);

        // Show loading state
        $('#vedmg-schedule-lab-modal .vedmg-modal-body').addClass('loading');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_get_schedule_course_data',
                course_id: courseId,
                student_id: studentId,
                nonce: '<?php echo wp_create_nonce("vedmg_classroom_nonce"); ?>'
            },
            success: function(response) {
                console.log('Course data response:', response);

                if (response.success && response.data) {
                    var courseData = response.data.course;
                    var studentData = response.data.student;

                    // Populate hidden fields
                    $('#lab-calendar-id').val(courseData.calendar_id || '');
                    $('#lab-instructor-email').val(courseData.instructor_email || '');

                    // Populate student info if available
                    if (studentData) {
                        $('#lab-student-email').val(studentData.email || '');
                        $('#lab-student-name').text(studentData.name || 'Unknown Student');
                        $('#lab-student-email-display').text(studentData.email || 'No email');
                    }

                    // Populate meeting link display
                    if (courseData.meeting_link) {
                        $('#lab-meeting-link-display').val(courseData.meeting_link);
                    } else {
                        $('#lab-meeting-link-display').val('No meeting link set for this course');
                    }

                    // Set default session title
                    if (courseData.course_name) {
                        $('#lab-session-summary').val('Live Session - ' + courseData.course_name);
                    }

                    // Set default dates (tomorrow for start, same day for end)
                    var tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    var dateString = tomorrow.toISOString().split('T')[0];
                    $('#lab-session-date').val(dateString);
                    $('#lab-end-date').val(dateString); // Same day by default

                    // Set default times
                    $('#lab-start-time').val('14:00');
                    $('#lab-end-time').val('15:00');

                    console.log('Modal populated with course data');

                } else {
                    console.error('Failed to fetch course data:', response.data);
                    alert('Error loading course data: ' + (response.data || 'Unknown error'));
                }

                // Remove loading state and show modal
                $('#vedmg-schedule-lab-modal .vedmg-modal-body').removeClass('loading');
                $('#vedmg-schedule-lab-modal').fadeIn();
            },
            error: function(xhr, status, error) {
                console.error('AJAX error fetching course data:', error);
                alert('Error loading course data. Please try again.');

                // Remove loading state
                $('#vedmg-schedule-lab-modal .vedmg-modal-body').removeClass('loading');
            }
        });
    }

    // Handle recurring session checkbox change
    $('#lab-recurring').on('change', function() {
        var isChecked = $(this).is(':checked');
        var $recurringOptions = $('#lab-recurring-options');
        var $recurringFrequency = $('#lab-recurring-frequency');
        var $recurringCount = $('#lab-recurring-count');

        if (isChecked) {
            $recurringOptions.slideDown();
            // Make fields required
            $recurringCount.prop('required', true);
            // Trigger change to update days display and frequency preview
            $recurringFrequency.trigger('change');
        } else {
            $recurringOptions.slideUp();
            // Remove required attribute
            $recurringCount.prop('required', false);
            // Hide all frequency-specific sections
            $('#weekly-days-section').hide();
            // Reset frequency preview
            updateFrequencyPreview();
            // Clear any selected days
            $('input[name="recurring_days[]"]').prop('checked', false);
        }
    });
    
    // Handle recurring frequency dropdown change
    $('#lab-recurring-frequency').on('change', function() {
        var selectedFrequency = $(this).val();
        var $weeklyDaysSection = $('#weekly-days-section');
        var $monthlySelectionSection = $('#monthly-selection-section');

        console.log('🔄 Frequency changed to:', selectedFrequency);

        // Hide all frequency-specific sections first
        $weeklyDaysSection.hide();
        $monthlySelectionSection.hide();
        $('#monthly-dates-section').hide();

        // Show relevant section based on frequency selection
        if (selectedFrequency === 'WEEKLY') {
            console.log('📅 Showing weekly days selection');
            $weeklyDaysSection.show();

            // Auto-select some default days if none are selected
            if ($('input[name="recurring_days[]"]:checked').length === 0) {
                // Default to Monday, Wednesday, Friday
                $('input[name="recurring_days[]"][value="MO"]').prop('checked', true);
                $('input[name="recurring_days[]"][value="WE"]').prop('checked', true);
                $('input[name="recurring_days[]"][value="FR"]').prop('checked', true);
                console.log('📅 Auto-selected default days: MO, WE, FR');
            }

            // Clear monthly selections
            $('#lab-recurring-month').val('');
            $('input[name="recurring_dates[]"]').prop('checked', false);

        } else if (selectedFrequency === 'DAILY') {
            console.log('📅 Daily frequency - no additional options needed');
            // Clear all selections since they're not relevant for daily
            $('input[name="recurring_days[]"]').prop('checked', false);
            $('#lab-recurring-month').val('');
            $('input[name="recurring_dates[]"]').prop('checked', false);

        } else if (selectedFrequency === 'MONTHLY') {
            console.log('📅 Monthly frequency - showing month and dates selection');
            $monthlySelectionSection.show();

            // Clear weekly selections since they're not relevant
            $('input[name="recurring_days[]"]').prop('checked', false);

            // Update sessions limit display
            updateSessionsLimit();
        }

        // Update the frequency preview and API body
        updateFrequencyPreview();
        updateApiBodyPreview();
    });
    
    // Handle changes to recurring days checkboxes
    $(document).on('change', 'input[name="recurring_days[]"]', function() {
        console.log('📅 Recurring days changed');
        updateFrequencyPreview();
        updateApiBodyPreview();
    });

    // Handle changes to recurring count
    $('#lab-recurring-count').on('input', function() {
        console.log('🔢 Recurring count changed to:', $(this).val());
        updateSessionsLimit();
        updateFrequencyPreview();
        updateApiBodyPreview();
    });

    // Handle month selection change
    $('#lab-recurring-month').on('change', function() {
        var selectedMonth = $(this).val();
        console.log('📅 Month changed to:', selectedMonth);

        if (selectedMonth) {
            $('#monthly-dates-section').slideDown();
            // Clear any previously selected dates
            $('input[name="recurring_dates[]"]').prop('checked', false);
            updateSelectedDatesCount();
        } else {
            $('#monthly-dates-section').slideUp();
        }

        updateFrequencyPreview();
        updateApiBodyPreview();
    });

    // Handle monthly dates selection with limit
    $(document).on('change', 'input[name="recurring_dates[]"]', function() {
        var maxSessions = parseInt($('#lab-recurring-count').val()) || 3;
        var selectedDates = $('input[name="recurring_dates[]"]:checked').length;

        console.log('📅 Date selection changed. Selected:', selectedDates, 'Max:', maxSessions);

        if (selectedDates > maxSessions) {
            // Uncheck this checkbox if limit exceeded
            $(this).prop('checked', false);
            alert('You can only select up to ' + maxSessions + ' dates based on the number of sessions.');
            return;
        }

        updateSelectedDatesCount();
        updateFrequencyPreview();
        updateApiBodyPreview();
    });

    // Function to update sessions limit display
    function updateSessionsLimit() {
        var count = parseInt($('#lab-recurring-count').val()) || 3;
        $('#max-sessions-count').text(count);
        $('#max-sessions-text').text(count);

        // Update selected dates count
        updateSelectedDatesCount();

        // Disable date checkboxes if limit reached
        var selectedDates = $('input[name="recurring_dates[]"]:checked').length;
        $('input[name="recurring_dates[]"]').each(function() {
            var $parent = $(this).closest('.vedmg-date-option');
            if (!$(this).is(':checked') && selectedDates >= count) {
                $parent.addClass('disabled');
                $(this).prop('disabled', true);
            } else {
                $parent.removeClass('disabled');
                $(this).prop('disabled', false);
            }
        });
    }

    // Function to update selected dates count
    function updateSelectedDatesCount() {
        var selectedCount = $('input[name="recurring_dates[]"]:checked').length;
        $('#selected-dates-count').text(selectedCount);
    }

    // Function to update the frequency preview
    function updateFrequencyPreview() {
        var frequency = $('#lab-recurring-frequency').val();
        var count = $('#lab-recurring-count').val() || 3;
        var selectedDays = [];
        var selectedDates = [];
        var selectedMonth = $('#lab-recurring-month').val();

        // Get selected days for weekly frequency
        if (frequency === 'WEEKLY') {
            $('input[name="recurring_days[]"]:checked').each(function() {
                selectedDays.push($(this).val());
            });
        }

        // Get selected dates for monthly frequency
        if (frequency === 'MONTHLY') {
            $('input[name="recurring_dates[]"]:checked').each(function() {
                selectedDates.push($(this).val());
            });
        }

        // Build frequency string
        var frequencyString = '';
        if (frequency && $('#lab-recurring').is(':checked')) {
            frequencyString = 'FREQ=' + frequency;

            if (frequency === 'WEEKLY' && selectedDays.length > 0) {
                frequencyString += ';BYDAY=' + selectedDays.join(',');
            } else if (frequency === 'MONTHLY' && selectedDates.length > 0 && selectedMonth) {
                frequencyString += ';BYMONTHDAY=' + selectedDates.join(',');
                frequencyString += ';BYMONTH=' + selectedMonth;
            }

            frequencyString += ';COUNT=' + count;
        } else {
            frequencyString = 'Single session (no recurrence)';
        }

        $('#lab-frequency-preview').text(frequencyString);
        console.log('🔄 Updated frequency preview:', frequencyString);
    }

    // Function to update real-time API body preview
    function updateApiBodyPreview() {
        // Get form values
        var summary = $('#lab-session-summary').val() || 'Enter session title above';
        var description = $('#lab-description').val() || 'Enter description above';
        var meetingLink = $('#lab-meeting-link-display').val() || 'Meeting link will be loaded';
        var sessionDate = $('#lab-session-date').val() || 'YYYY-MM-DD';
        var endDate = $('#lab-end-date').val() || 'YYYY-MM-DD';
        var startTime = $('#lab-start-time').val() || 'HH:MM';
        var endTime = $('#lab-end-time').val() || 'HH:MM';
        var timezone = $('#lab-timezone').val() || '+05:30';
        var calendarId = $('#lab-calendar-id').val() || 'will be loaded from course';
        var studentEmail = $('#lab-student-email').val() || 'will be loaded from student';
        var instructorEmail = $('#lab-instructor-email').val() || 'will be loaded from course';

        // Build datetime strings with separate dates
        var startDateTime = sessionDate + 'T' + startTime + ':00' + timezone;
        var endDateTime = endDate + 'T' + endTime + ':00' + timezone;

        // Build description with meeting link
        var fullDescription = description;
        if (meetingLink && meetingLink !== 'Meeting link will be loaded') {
            fullDescription += '. Please join at ' + meetingLink;
        } else {
            fullDescription += '. Meeting link will be appended.';
        }

        // Get frequency string
        var frequency = $('#lab-recurring-frequency').val();
        var count = $('#lab-recurring-count').val() || 3;
        var frequencyString = '';

        if (frequency && $('#lab-recurring').is(':checked')) {
            frequencyString = 'FREQ=' + frequency;

            if (frequency === 'WEEKLY') {
                var selectedDays = [];
                $('input[name="recurring_days[]"]:checked').each(function() {
                    selectedDays.push($(this).val());
                });
                if (selectedDays.length > 0) {
                    frequencyString += ';BYDAY=' + selectedDays.join(',');
                }
            } else if (frequency === 'MONTHLY') {
                var selectedDates = [];
                $('input[name="recurring_dates[]"]:checked').each(function() {
                    selectedDates.push($(this).val());
                });
                var selectedMonth = $('#lab-recurring-month').val();
                if (selectedDates.length > 0 && selectedMonth) {
                    frequencyString += ';BYMONTHDAY=' + selectedDates.join(',');
                    frequencyString += ';BYMONTH=' + selectedMonth;
                }
            }

            frequencyString += ';COUNT=' + count;
        } else {
            frequencyString = 'Single session (no recurrence)';
        }

        // Build API body object
        var apiBody = {
            "calendar_id": calendarId,
            "user_email": studentEmail,
            "summary": summary,
            "location": "On Google Meet",
            "description": fullDescription,
            "start_datetime": startDateTime,
            "end_datetime": endDateTime,
            "instructor_email": instructorEmail,
            "meeting_frequency": frequencyString
        };

        // Update the preview
        $('#api-body-preview').text(JSON.stringify(apiBody, null, 2));
        console.log('📡 Updated API body preview');
    }

    // Handle same day checkbox
    $('#lab-same-day').on('change', function() {
        var isChecked = $(this).is(':checked');
        var $endDate = $('#lab-end-date');
        var $startDate = $('#lab-session-date');

        if (isChecked) {
            // Auto-set end date to match start date
            $endDate.val($startDate.val());
            $endDate.prop('readonly', true);
        } else {
            $endDate.prop('readonly', false);
        }
        updateApiBodyPreview();
    });

    // Auto-update end date when start date changes (if same day is checked)
    $('#lab-session-date').on('change', function() {
        if ($('#lab-same-day').is(':checked')) {
            $('#lab-end-date').val($(this).val());
        }
        updateApiBodyPreview();
    });

    // Add event listeners for real-time API preview updates
    $('#lab-session-summary, #lab-description, #lab-session-date, #lab-start-time, #lab-end-date, #lab-end-time, #lab-timezone').on('input change', function() {
        updateApiBodyPreview();
    });

    // Initialize recurring options state
    if ($('#lab-recurring').is(':checked')) {
        $('#lab-recurring-options').show();
        $('#lab-recurring-frequency').trigger('change');
    } else {
        $('#lab-recurring-options').hide();
    }

    // Initialize previews
    updateFrequencyPreview();
    updateApiBodyPreview();
    
    // Handle form submission for scheduling sessions
    $('#vedmg-schedule-lab-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $submitBtn = $('#save-schedule-lab');

        // Basic form validation
        var sessionDate = $('#lab-session-date').val();
        var endDate = $('#lab-end-date').val();
        var startTime = $('#lab-start-time').val();
        var endTime = $('#lab-end-time').val();
        var summary = $('#lab-session-summary').val();

        // Validate required fields with professional notifications
        if (!sessionDate) {
            if (window.VedMGNotifications) {
                VedMGNotifications.showError('Validation Error', 'Please select a start date.');
            } else {
                alert('Please select a start date.');
            }
            $('#lab-session-date').focus();
            return;
        }

        if (!endDate) {
            if (window.VedMGNotifications) {
                VedMGNotifications.showError('Validation Error', 'Please select an end date.');
            } else {
                alert('Please select an end date.');
            }
            $('#lab-end-date').focus();
            return;
        }

        if (!startTime) {
            if (window.VedMGNotifications) {
                VedMGNotifications.showError('Validation Error', 'Please select a start time.');
            } else {
                alert('Please select a start time.');
            }
            $('#lab-start-time').focus();
            return;
        }

        if (!endTime) {
            if (window.VedMGNotifications) {
                VedMGNotifications.showError('Validation Error', 'Please select an end time.');
            } else {
                alert('Please select an end time.');
            }
            $('#lab-end-time').focus();
            return;
        }

        if (!summary || summary.trim() === '') {
            if (window.VedMGNotifications) {
                VedMGNotifications.showError('Validation Error', 'Please enter a session title.');
            } else {
                alert('Please enter a session title.');
            }
            $('#lab-session-summary').focus();
            return;
        }

        // Enhanced validation for recurring sessions
        var isRecurring = $('#lab-recurring').is(':checked');
        if (isRecurring) {
            var recurringCount = parseInt($('#lab-recurring-count').val());
            var recurringFrequency = $('#lab-recurring-frequency').val();

            // Validate number of sessions
            if (!recurringCount || recurringCount < 1) {
                if (window.VedMGNotifications) {
                    VedMGNotifications.showError('Validation Error', 'Please enter a valid number of sessions (minimum 1).');
                } else {
                    alert('Please enter a valid number of sessions (minimum 1).');
                }
                $('#lab-recurring-count').focus();
                return;
            }

            if (recurringCount > 100) {
                if (window.VedMGNotifications) {
                    VedMGNotifications.showError('Validation Error', 'Number of sessions cannot exceed 100 for safety reasons.');
                } else {
                    alert('Number of sessions cannot exceed 100 for safety reasons.');
                }
                $('#lab-recurring-count').focus();
                return;
            }

            // Validate frequency
            if (!recurringFrequency) {
                if (window.VedMGNotifications) {
                    VedMGNotifications.showError('Validation Error', 'Please select a recurring frequency.');
                } else {
                    alert('Please select a recurring frequency.');
                }
                $('#lab-recurring-frequency').focus();
                return;
            }

            // Validate weekly recurring days
            if (recurringFrequency === 'WEEKLY') {
                if ($('input[name="recurring_days[]"]:checked').length === 0) {
                    if (window.VedMGNotifications) {
                        VedMGNotifications.showError('Validation Error', 'Please select at least one day of the week for weekly sessions.');
                    } else {
                        alert('Please select at least one day of the week for weekly sessions.');
                    }
                    return;
                }
            }
        }

        // Show loading notification
        if (window.VedMGNotifications) {
            VedMGNotifications.showLoading('Scheduling lab session...');
        }

        // Disable submit button and show loading
        $submitBtn.prop('disabled', true).text('Scheduling...');

        // Collect form data
        var formData = new FormData($form[0]);
        formData.append('action', 'vedmg_schedule_lab');
        formData.append('nonce', '<?php echo wp_create_nonce("vedmg_classroom_nonce"); ?>');

        // Collect selected student IDs if it's a group session
        if ($('#lab-session-type').val() === 'group') {
            var selectedStudents = [];
            $('.enrollment-checkbox:checked').each(function() {
                selectedStudents.push($(this).data('student-id'));
            });
            formData.append('student_ids', JSON.stringify(selectedStudents));
            console.log('Group session - selected students:', selectedStudents);
        }

        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // CRITICAL FIX: Do everything in the correct order with proper delays

                    // Step 1: Hide loading notification immediately
                    if (window.VedMGNotifications) {
                        VedMGNotifications.hide();
                    }

                    // Step 2: Close modal after a short delay
                    setTimeout(function() {
                        $('#vedmg-schedule-lab-modal').fadeOut();
                    }, 200);

                    // Step 3: Show success notification after modal operations are complete
                    setTimeout(function() {
                        if (window.VedMGNotifications) {
                            console.log('=== SHOWING SUCCESS NOTIFICATION ===');
                            const sessionType = response.data ? response.data.type : 'session';
                            const studentsCount = response.data ? response.data.students_notified : 1;

                            // Create a completely isolated notification that cannot be interfered with
                            VedMGNotifications.showSuccess(
                                'Session Scheduled Successfully!',
                                `${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} session scheduled for ${studentsCount} student(s). Calendar invitations have been sent.`,
                                true // Reload page when user clicks OK
                            );
                            console.log('=== SUCCESS NOTIFICATION DISPLAYED ===');
                        } else {
                            alert('Session scheduled successfully!');
                            window.location.reload();
                        }
                    }, 800); // Longer delay to ensure no interference

                    // Reset form
                    $form[0].reset();
                    $('#lab-recurring-options').hide();

                    // Update button states based on response
                    if (response.data.session_id && response.data.affected_students) {
                        // Update schedule button state for affected students
                        response.data.affected_students.forEach(function(studentId) {
                            var $button = $('.vedmg-schedule-lab-btn[data-student-id="' + studentId + '"]');
                            if ($button.length > 0) {
                                $button.removeClass('vedmg-classroom-btn-accent')
                                       .addClass('vedmg-classroom-btn-success scheduled');
                                $button.find('.vedmg-btn-text').text('Reschedule Lab');
                                $button.find('.vedmg-btn-icon').text('🔄');
                                $button.attr('data-has-sessions', '1');
                            }
                        });
                    }

                    // Page will reload only when user closes the success notification
                    // No automatic reload to keep notification visible

                } else {
                    // Show professional error notification
                    if (window.VedMGNotifications) {
                        VedMGNotifications.showError('Scheduling Failed', 'Error scheduling session: ' + (response.data || 'Unknown error'));
                    } else {
                        alert('Error scheduling session: ' + (response.data || 'Unknown error'));
                    }
                }
            },
            error: function(xhr, status, error) {
                // Hide loading notification
                if (window.VedMGNotifications) {
                    VedMGNotifications.hide();
                }

                // Show professional error notification
                if (window.VedMGNotifications) {
                    VedMGNotifications.showError('Scheduling Failed', 'Error scheduling session: ' + error);
                } else {
                    alert('Error scheduling session: ' + error);
                }
            },
            complete: function() {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).text('Schedule Session');
            }
        });
    });
    
    // Function to update schedule button states
    function updateScheduleButtonState(sessionId, affectedStudents) {
        affectedStudents.forEach(function(studentId) {
            // Update the Schedule Lab button
            var $scheduleBtn = $('.vedmg-schedule-lab-btn[data-student-id="' + studentId + '"]');
            $scheduleBtn.removeClass('vedmg-classroom-btn-accent')
                       .addClass('vedmg-classroom-btn-success vedmg-scheduled-btn')
                       .text('Scheduled')
                       .data('session-id', sessionId);
            
            // Update the Scheduled button to active state
            var $scheduledBtn = $('.vedmg-scheduled-btn-inactive[data-student-id="' + studentId + '"], .vedmg-scheduled-btn[data-student-id="' + studentId + '"]');
            $scheduledBtn.removeClass('vedmg-classroom-btn-danger vedmg-scheduled-btn-inactive')
                        .addClass('vedmg-classroom-btn-success vedmg-scheduled-btn')
                        .text('Scheduled')
                        .attr('data-has-sessions', '1')
                        .data('student-id', studentId);
        });
    }
    
    // Handle click on "Scheduled" button to show session details
    $(document).on('click', '.vedmg-scheduled-btn, .vedmg-scheduled-btn-inactive', function(e) {
        e.preventDefault();
        var studentId = $(this).data('student-id');
        var hasSessions = $(this).data('has-sessions');
        
        if (hasSessions === '1' || hasSessions === 1) {
            // Student has sessions, show details modal
            showSessionDetailsModal(studentId);
        } else {
            // Student has no sessions, show message
            alert('No sessions scheduled for this student yet. Use "Schedule Lab" button to create sessions.');
        }
    });
    
    // Function to show session details modal
    function showSessionDetailsModal(studentId) {
        var $modal = $('#vedmg-session-details-modal');
        var $content = $('#session-details-content');
        
        // Show loading state
        $content.html('<div class="vedmg-loading">Loading session details...</div>');
        $modal.fadeIn();
        
        // Fetch session details via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_get_session_details',
                student_id: studentId,
                nonce: '<?php echo wp_create_nonce("vedmg_get_session_details"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displaySessionDetails(response.data.sessions);
                } else {
                    $content.html('<div class="vedmg-error">Error: ' + response.data + '</div>');
                }
            },
            error: function() {
                $content.html('<div class="vedmg-error">Failed to load session details. Please try again.</div>');
            }
        });
    }
    
    // Function to display session details in the modal
    function displaySessionDetails(sessions) {
        var $content = $('#session-details-content');
        var html = '';
        
        if (!sessions || sessions.length === 0) {
            html = '<div class="vedmg-info">No sessions found for this student.</div>';
        } else {
            html += '<div class="vedmg-session-summary">';
            html += '<h4>Total Sessions: ' + sessions.length + '</h4>';
            html += '</div>';
            
            sessions.forEach(function(session, index) {
                html += '<div class="vedmg-session-detail-card">';
                html += '<div class="vedmg-session-header">';
                html += '<h4>' + session.session_title + '</h4>';
                html += '<span class="vedmg-session-type">' + session.session_type + '</span>';
                html += '</div>';
                
                html += '<div class="vedmg-session-info">';
                html += '<div class="vedmg-info-grid">';
                
                // Basic session info
                html += '<div class="vedmg-info-item">';
                html += '<strong>Date:</strong> ' + session.session_date;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Time:</strong> ' + session.session_time;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Duration:</strong> ' + session.duration;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Instructor:</strong> ' + session.instructor;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Course:</strong> ' + session.course_name;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Total Enrolled:</strong> ' + session.total_enrolled;
                html += '</div>';
                
                // Recurring info
                if (session.is_recurring === 'Yes') {
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>Recurring:</strong> ' + session.recurring_pattern;
                    html += '</div>';
                    
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>Sessions Count:</strong> ' + session.recurring_count;
                    html += '</div>';
                    
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>End Date:</strong> ' + session.recurring_end_date;
                    html += '</div>';
                }
                
                html += '</div>'; // End info-grid
                
                // Description
                if (session.description && session.description !== 'No description provided') {
                    html += '<div class="vedmg-session-description">';
                    html += '<strong>Description:</strong><br>' + session.description;
                    html += '</div>';
                }
                
                // Enrolled students
                if (session.enrolled_students && session.enrolled_students.length > 0) {
                    html += '<div class="vedmg-enrolled-students">';
                    html += '<strong>Enrolled Students:</strong>';
                    html += '<ul>';
                    session.enrolled_students.forEach(function(student) {
                        html += '<li>' + student.student_name + ' (' + student.student_email + ')</li>';
                    });
                    html += '</ul>';
                    html += '</div>';
                }
                
                html += '</div>'; // End session-info
                html += '</div>'; // End session-detail-card
            });
        }
        
        $content.html(html);
    }
    
    // Handle modal close events
    $('#close-session-details, #close-session-details-btn').on('click', function() {
        $('#vedmg-session-details-modal').fadeOut();
    });
    
    // Close modal when clicking outside
    $('#vedmg-session-details-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).fadeOut();
        }
    });
    
    // Classroom Management Functions

    // Handle Create Classroom button
    $('.vedmg-create-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const courseName = button.data('course-name');
        const instructorEmail = button.data('instructor-email');
        const instructorName = button.data('instructor-name');

        if (!confirm(`Are you sure you want to create a Google Classroom for "${courseName}"?`)) {
            return;
        }

        // Show loading state
        button.prop('disabled', true);
        button.find('.vedmg-classroom-spinner').addClass('spinning');
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Creating...');

        // Show overlay loader
        showLoadingOverlay('Creating Google Classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_create_classroom',
                course_id: courseId,
                course_name: courseName,
                instructor_email: instructorEmail,
                instructor_name: instructorName,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    let message = 'Classroom created successfully!\n\n' +
                                  'Classroom ID: ' + response.data.classroom_id + '\n' +
                                  'Enrollment Code: ' + (response.data.invitation_code || 'N/A') + '\n' +
                                  'Course State: ' + (response.data.course_state || 'ACTIVE') + '\n' +
                                  'Classroom Link: ' + (response.data.classroom_link || 'N/A');

                    alert(message);
                    location.reload(); // Refresh page to show updated status
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred while creating the classroom.');
            },
            complete: function() {
                button.prop('disabled', false);
                button.find('.vedmg-classroom-spinner').removeClass('spinning');
                button.html(originalText);
                hideLoadingOverlay();
            }
        });
    });

    // Handle Archive Classroom button
    $('.vedmg-archive-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const classroomId = button.data('classroom-id');

        if (!confirm('Are you sure you want to archive this classroom? Students will no longer be able to access it.')) {
            return;
        }

        // Show loading state
        button.prop('disabled', true);
        button.find('.vedmg-classroom-spinner').addClass('spinning');
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Archiving...');

        // Show overlay loader
        showLoadingOverlay('Archiving Google Classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_archive_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    let message = 'Classroom archived successfully!';

                    // Add development mode notice if using mock response
                    if (response.data.mock_response) {
                        message += '\n\n⚠️ DEVELOPMENT MODE: This is a mock response for testing. Please archive the classroom manually in Google Classroom.';
                    }

                    alert(message);
                    location.reload(); // Refresh page to show updated status
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred while archiving the classroom.');
            },
            complete: function() {
                button.prop('disabled', false);
                button.find('.vedmg-classroom-spinner').removeClass('spinning');
                button.html(originalText);
                hideLoadingOverlay();
            }
        });
    });

    // Handle Delete Classroom button with two-step process
    $('.vedmg-delete-classroom-btn').on('click', function() {
        const button = $(this);
        const courseId = button.data('course-id');
        const classroomId = button.data('classroom-id');

        // Step 1: Show initial confirmation popup
        showCustomConfirmation(
            'Delete Classroom',
            'Are you sure you want to delete this classroom? This will first archive the classroom and then permanently delete it.',
            'Delete Classroom',
            'Cancel',
            function() {
                // User confirmed, start the two-step deletion process
                startTwoStepDeletion(button, courseId, classroomId);
            }
        );
    });

    // Two-step deletion process
    function startTwoStepDeletion(button, courseId, classroomId) {
        // Step 1: Archive the classroom first
        button.prop('disabled', true);
        const originalText = button.text();
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Archiving...');

        showLoadingOverlay('Archiving classroom before deletion...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_archive_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                hideLoadingOverlay();

                if (response.success) {
                    // Step 2: Show archive success and ask for final deletion confirmation
                    showCustomConfirmation(
                        'Classroom Archived',
                        'The classroom has been successfully archived. Do you want to permanently delete it now? This action cannot be undone.',
                        'Delete Permanently',
                        'Keep Archived',
                        function() {
                            // User confirmed final deletion
                            performFinalDeletion(button, courseId, classroomId, originalText);
                        },
                        function() {
                            // User chose to keep archived
                            button.prop('disabled', false);
                            button.html(originalText);
                            location.reload(); // Refresh to show archived status
                        }
                    );
                } else {
                    button.prop('disabled', false);
                    button.html(originalText);
                    showCustomAlert('Archive Failed', 'Failed to archive classroom: ' + response.data);
                }
            },
            error: function() {
                hideLoadingOverlay();
                button.prop('disabled', false);
                button.html(originalText);
                showCustomAlert('Error', 'An error occurred while archiving the classroom.');
            }
        });
    }

    // Perform final deletion after archiving
    function performFinalDeletion(button, courseId, classroomId, originalText) {
        button.html('<span class="vedmg-classroom-spinner spinning"></span> Deleting...');
        showLoadingOverlay('Permanently deleting classroom...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_delete_classroom',
                course_id: courseId,
                classroom_id: classroomId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                hideLoadingOverlay();

                if (response.success) {
                    showCustomAlert('Success', 'Classroom deleted successfully!', function() {
                        location.reload();
                    });
                } else {
                    button.prop('disabled', false);
                    button.html(originalText);
                    showCustomAlert('Delete Failed', 'Failed to delete classroom: ' + response.data);
                }
            },
            error: function() {
                hideLoadingOverlay();
                button.prop('disabled', false);
                button.html(originalText);
                showCustomAlert('Error', 'An error occurred while deleting the classroom.');
            }
        });
    }

    // Custom confirmation popup
    function showCustomConfirmation(title, message, confirmText, cancelText, onConfirm, onCancel) {
        const popup = $(`
            <div class="vedmg-custom-popup-overlay">
                <div class="vedmg-custom-popup">
                    <div class="vedmg-popup-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="vedmg-popup-body">
                        <p>${message}</p>
                    </div>
                    <div class="vedmg-popup-footer">
                        <button class="vedmg-popup-btn vedmg-popup-btn-cancel">${cancelText}</button>
                        <button class="vedmg-popup-btn vedmg-popup-btn-confirm">${confirmText}</button>
                    </div>
                </div>
            </div>
        `);

        $('body').append(popup);
        popup.addClass('show').fadeIn(300);

        // Ensure popup is centered and visible
        setTimeout(function() {
            popup.css('display', 'flex');
        }, 10);

        popup.find('.vedmg-popup-btn-confirm').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onConfirm) onConfirm();
        });

        popup.find('.vedmg-popup-btn-cancel').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onCancel) onCancel();
        });

        // Close on overlay click
        popup.on('click', function(e) {
            if (e.target === popup[0]) {
                popup.fadeOut(300, function() { popup.remove(); });
                if (onCancel) onCancel();
            }
        });
    }

    // Custom alert popup
    function showCustomAlert(title, message, onClose) {
        const popup = $(`
            <div class="vedmg-custom-popup-overlay">
                <div class="vedmg-custom-popup">
                    <div class="vedmg-popup-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="vedmg-popup-body">
                        <p>${message}</p>
                    </div>
                    <div class="vedmg-popup-footer">
                        <button class="vedmg-popup-btn vedmg-popup-btn-primary">OK</button>
                    </div>
                </div>
            </div>
        `);

        $('body').append(popup);
        popup.addClass('show').fadeIn(300);

        // Ensure popup is centered and visible
        setTimeout(function() {
            popup.css('display', 'flex');
        }, 10);

        popup.find('.vedmg-popup-btn-primary').on('click', function() {
            popup.fadeOut(300, function() { popup.remove(); });
            if (onClose) onClose();
        });

        // Close on overlay click
        popup.on('click', function(e) {
            if (e.target === popup[0]) {
                popup.fadeOut(300, function() { popup.remove(); });
                if (onClose) onClose();
            }
        });
    }

    // Loading overlay functions
    function showLoadingOverlay(message) {
        const overlay = $('<div class="vedmg-loading-overlay">' +
            '<div class="vedmg-loading-content">' +
            '<div class="vedmg-loading-spinner"></div>' +
            '<div class="vedmg-loading-message">' + message + '</div>' +
            '</div>' +
            '</div>');
        $('body').append(overlay);
        overlay.fadeIn(300);
    }

    function hideLoadingOverlay() {
        $('.vedmg-loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    // Professional Notification System (v2.0)
    window.VedMGNotifications = {
        show: function(type, title, message, actions = null) {
            console.log('=== VedMGNotifications.show START ===');
            console.log('Type:', type, 'Title:', title);

            // CRITICAL: Remove existing notifications and ensure clean slate
            $('.vedmg-notification-overlay').remove();
            $('.vedmg-loading-overlay').remove(); // Also remove any loading overlays

            // Add a small delay to ensure DOM is clean
            setTimeout(function() {
                VedMGNotifications._createAndShowNotification(type, title, message, actions);
            }, 50);
        },

        _createAndShowNotification: function(type, title, message, actions) {
            console.log('=== Creating notification overlay ===');

            let iconHtml = '';
            switch(type) {
                case 'success':
                    iconHtml = '<div class="vedmg-notification-icon success">✅</div>';
                    break;
                case 'error':
                    iconHtml = '<div class="vedmg-notification-icon error">❌</div>';
                    break;
                case 'loading':
                    iconHtml = '<div class="vedmg-notification-icon loading">⏳</div>';
                    break;
                default:
                    iconHtml = '<div class="vedmg-notification-icon">ℹ️</div>';
            }

            let actionsHtml = '';
            if (actions) {
                actionsHtml = '<div class="vedmg-notification-actions">';
                actions.forEach(action => {
                    actionsHtml += `<button class="vedmg-notification-btn ${action.class}" data-action="${action.action}">${action.text}</button>`;
                });
                actionsHtml += '</div>';
            } else if (type !== 'loading') {
                actionsHtml = '<div class="vedmg-notification-actions"><button class="vedmg-notification-btn primary" data-action="close">OK</button></div>';
            }

            const overlay = $(`
                <div class="vedmg-notification-overlay">
                    <div class="vedmg-notification-modal">
                        ${iconHtml}
                        <div class="vedmg-notification-title">${title}</div>
                        <div class="vedmg-notification-message">${message}</div>
                        ${actionsHtml}
                    </div>
                </div>
            `);

            $('body').append(overlay);
            // Force reflow to ensure proper centering
            overlay[0].offsetHeight;
            overlay.addClass('show');

            // CRITICAL: Mark this notification as protected from immediate hiding
            overlay.attr('data-protected', 'true');

            console.log('=== NOTIFICATION OVERLAY CREATED AND SHOWN ===');
            console.log('Overlay element:', overlay[0]);
            console.log('Overlay classes:', overlay[0].className);

            // Extra protection: prevent immediate hiding for 2 seconds
            setTimeout(function() {
                overlay.removeAttr('data-protected');
                console.log('Notification protection removed - can now be closed by user');
            }, 2000);

            // Handle button clicks
            overlay.find('.vedmg-notification-btn').on('click', function() {
                const action = $(this).data('action');
                if (action === 'close') {
                    VedMGNotifications.hide();
                } else if (actions) {
                    const actionObj = actions.find(a => a.action === action);
                    if (actionObj && actionObj.callback) {
                        actionObj.callback();
                    }
                }
            });

            // Handle click outside modal to close
            overlay.on('click', function(e) {
                if (e.target === overlay[0]) {
                    VedMGNotifications.hide();
                }
            });

            // All messages (success and error) stay until user manually closes them
            // No auto-hide for any message type
        },

        hide: function() {
            console.log('=== VedMGNotifications.hide called ===');

            // Check if notification is protected
            const $overlay = $('.vedmg-notification-overlay');
            if ($overlay.attr('data-protected') === 'true') {
                console.log('⚠️  Notification is protected - ignoring hide request');
                return;
            }

            console.log('Proceeding with hide...');
            $overlay.removeClass('show');
            setTimeout(function() {
                $overlay.remove();
                console.log('=== Notification overlay removed ===');
            }, 300);
        },

        showLoading: function(message = 'Processing...') {
            this.show('loading', 'Please Wait', message);
        },

        showSuccess: function(title, message, reloadOnClose = false) {
            const actions = [{
                text: 'OK',
                class: 'primary',
                action: 'close',
                callback: function() {
                    VedMGNotifications.hide();
                    if (reloadOnClose) {
                        window.location.reload();
                    }
                }
            }];
            this.show('success', title, message, actions);
        },

        showError: function(title, message) {
            this.show('error', title, message);
        }
    };

    // Any additional enrollment-specific JavaScript will be handled by enrollments.js
});
</script>

<!-- Professional Notification Styles -->
<style>
/* Enhanced Schedule Lab Button Styles */
.vedmg-schedule-lab-btn {
    position: relative;
    transition: all 0.3s ease;
}

.vedmg-schedule-lab-btn.scheduled {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

.vedmg-schedule-lab-btn.scheduled:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}

.vedmg-btn-icon {
    margin-right: 5px;
}

.vedmg-session-count {
    font-size: 0.8em;
    margin-left: 5px;
    opacity: 0.8;
}

/* Professional Notification System */
.vedmg-notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.vedmg-notification-overlay.show {
    visibility: visible;
    opacity: 1;
}

.vedmg-notification-modal {
    background: white;
    border-radius: 8px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    animation: vedmgModalSlideIn 0.3s ease;
}

@keyframes vedmgModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vedmg-notification-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.vedmg-notification-icon.success {
    color: #28a745;
}

.vedmg-notification-icon.error {
    color: #dc3545;
}

.vedmg-notification-icon.loading {
    color: #007cba;
    animation: vedmgSpin 1s linear infinite;
}

@keyframes vedmgSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.vedmg-notification-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.vedmg-notification-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

.vedmg-notification-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.vedmg-notification-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.vedmg-notification-btn.primary {
    background-color: #007cba;
    color: white;
}

.vedmg-notification-btn.primary:hover {
    background-color: #005a87;
}

.vedmg-notification-btn.secondary {
    background-color: #6c757d;
    color: white;
}

.vedmg-notification-btn.secondary:hover {
    background-color: #545b62;
}
</style>
