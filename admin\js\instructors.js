/**
 * VedMG ClassRoom Instructors JavaScript
 * 
 * JavaScript functionality specific to the instructor roster page.
 * Handles instructor management, filtering, and instructor-related actions.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

(function($) {
    'use strict';

    /**
     * Instructors management specific functionality
     */
    var VedMGInstructors = {
        
        /**
         * Initialize instructor management functionality
         */
        init: function() {
            console.log('VedMG ClassRoom Instructors initialized');
            
            // Bind instructor specific events
            this.bindEvents();
            
            // Initialize instructor components
            this.initComponents();
            
            // Update instructor counts
            this.updateInstructorCounts();
        },
        
        /**
         * Bind instructor specific events
         */
        bindEvents: function() {
            // Instructor management actions
            $(document).on('click', '#add-instructor', this.handleAddInstructor);
            $(document).on('click', '.vedmg-view-instructor-btn', this.handleViewInstructor);
            $(document).on('click', '.vedmg-edit-instructor-btn', this.handleEditInstructor);
            
            // Modal events
            $(document).on('click', '.vedmg-modal-close, #cancel-instructor-edit, #cancel-class-reassignment', this.closeModals);
            $(document).on('click', '.vedmg-modal', function(e) {
                if (e.target === this) {
                    VedMGInstructors.closeModals();
                }
            });
            $(document).on('submit', '#vedmg-instructor-edit-form', this.handleSaveInstructor);
            
            // Class reassignment events
            $(document).on('click', '[data-action="reassign"]', this.handleShowReassignModal);
            $(document).on('submit', '#vedmg-class-reassignment-form', this.handleReassignClasses);
            $(document).on('change', 'input[name="reassignment_type"]', this.handleReassignTypeChange);
            $(document).on('click', '#select-all-sessions', this.handleSelectAllSessions);
            $(document).on('click', '#deselect-all-sessions', this.handleDeselectAllSessions);
            $(document).on('click', '#select-all-meetings', this.handleSelectAllMeetings);
            $(document).on('click', '#deselect-all-meetings', this.handleDeselectAllMeetings);
            $(document).on('change', '.vedmg-session-checkbox', this.handleSessionSelection);
            
            // Pagination events - DISABLED (using PHP pagination)
            // $(document).on('click', '.vedmg-pagination-btn', this.handlePagination);
            // $(document).on('change', '#pagination-size-select', this.handlePageSizeChange);
            
            // Search functionality
            $(document).on('input', '#search', this.handleSearchInput);
            
            // Bulk actions
            $(document).on('click', '#apply-instructor-bulk-action', this.handleBulkAction);
            $(document).on('change', '#select-all-instructors', this.handleSelectAll);
            $(document).on('change', '.instructor-checkbox', this.handleInstructorSelect);
            
            // Sync and refresh
            $(document).on('click', '#sync-masterstudy', this.handleSyncMasterStudy);
            $(document).on('click', '#refresh-instructors', this.handleRefreshInstructors);
            
            // View courses
            $(document).on('click', '.vedmg-view-courses', this.handleViewCourses);
            
            console.log('Instructor events bound');
        },
        
        /**
         * Initialize instructor components
         */
        initComponents: function() {
            // Initialize instructor table
            this.initInstructorTable();
            
            // Initialize search functionality
            this.initSearch();
            
            // Initialize pagination
            this.initPagination();
            
            console.log('Instructor components initialized');
        },
        
        /**
         * Initialize instructor table
         */
        initInstructorTable: function() {
            // Add row interactions
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var instructorId = $row.data('instructor-id');
                
                if (instructorId) {
                    $row.attr('data-instructor-id', instructorId);
                }
            });
        },
        
        /**
         * Initialize search functionality
         */
        initSearch: function() {
            // Set current search value from URL parameter
            var urlParams = new URLSearchParams(window.location.search);
            var currentSearch = urlParams.get('search') || '';
            $('#search').val(currentSearch);

            console.log('Search functionality initialized');
        },
        
        /**
         * Initialize pagination
         */
        initPagination: function() {
            // PHP pagination is now used - JavaScript pagination disabled
            console.log('PHP pagination enabled - JavaScript pagination disabled');
        },
        
        /**
         * Handle add instructor
         */
        handleAddInstructor: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $spinner = $button.find('.vedmg-classroom-spinner');
            
            // Show loading state
            $spinner.show();
            $button.prop('disabled', true);
            
            // Open instructor add form
            VedMGInstructors.openInstructorForm('add', null, function() {
                // Hide loading state
                $spinner.hide();
                $button.prop('disabled', false);
            });
            
            console.log('Adding new instructor');
        },
        
        /**
         * Handle view instructor details
         */
        handleViewInstructor: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var instructorId = $button.data('instructor-id');
            var $row = $button.closest('tr');
            var instructorData = VedMGInstructors.extractInstructorData($row);
            
            // Open instructor details modal
            VedMGInstructors.openInstructorDetailsModal(instructorData);
            
            console.log('Viewing instructor details:', instructorId);
        },
        
        /**
         * Handle edit instructor
         */
        handleEditInstructor: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var instructorId = $button.data('instructor-id');
            var $row = $button.closest('tr');
            var instructorData = VedMGInstructors.extractInstructorData($row);
            
            // Open instructor edit modal
            VedMGInstructors.openInstructorEditModal(instructorData);
            
            console.log('Editing instructor:', instructorId);
        },
        
        /**
         * Handle search input (server-side search)
         */
        handleSearchInput: function() {
            // For server-side search, we could add debouncing here
            // but for now, let users submit the form manually
            console.log('Search input changed - submit form to search');
        },
        
        /**
         * Handle bulk action
         */
        handleBulkAction: function(e) {
            e.preventDefault();
            
            var action = $('#instructor-bulk-action-select').val();
            var selectedInstructors = $('.instructor-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (!action) {
                alert('Please select a bulk action.');
                return;
            }
            
            if (selectedInstructors.length === 0) {
                alert('Please select at least one instructor.');
                return;
            }
            
            // Confirm bulk action
            var message = 'Perform "' + action + '" on ' + selectedInstructors.length + ' selected instructor(s)?';
            if (!confirm(message)) {
                return;
            }
            
            // Perform bulk action
            VedMGInstructors.performBulkAction(action, selectedInstructors);
        },
        
        /**
         * Handle select all instructors
         */
        handleSelectAll: function() {
            var isChecked = $(this).is(':checked');
            $('.instructor-checkbox').prop('checked', isChecked);
            VedMGInstructors.updateBulkActionState();
        },
        
        /**
         * Handle individual instructor selection
         */
        handleInstructorSelect: function() {
            VedMGInstructors.updateBulkActionState();
            
            // Update select all checkbox
            var totalCheckboxes = $('.instructor-checkbox').length;
            var checkedCheckboxes = $('.instructor-checkbox:checked').length;
            
            $('#select-all-instructors').prop('checked', totalCheckboxes === checkedCheckboxes);
        },
        
        /**
         * Handle sync with MasterStudy LMS
         */
        handleSyncMasterStudy: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            
            // Confirm sync
            var confirmed = confirm('Sync with MasterStudy LMS?\n\nThis will fetch the latest instructor data from MasterStudy LMS and update the database.');
            
            if (!confirmed) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Syncing...');
            
            // Perform sync
            VedMGInstructors.syncWithMasterStudy().then(function(result) {
                $button.prop('disabled', false);
                $button.text('Sync with MasterStudy LMS');
                
                VedMGClassRoomAdmin.showMessage('Sync completed! ' + result.message, 'success');
                
                // Refresh the page to show updated data
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
                
            }).catch(function(error) {
                console.error('Sync failed:', error);
                
                $button.prop('disabled', false);
                $button.text('Sync with MasterStudy LMS');
                
                VedMGClassRoomAdmin.showMessage('Sync failed. Please try again.', 'error');
            });
        },
        
        /**
         * Handle refresh instructors
         */
        handleRefreshInstructors: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Refreshing...');
            
            // Refresh instructors data
            VedMGInstructors.refreshInstructorsData().then(function() {
                $button.prop('disabled', false);
                $button.text('Refresh Data');
                
                // Update counts
                VedMGInstructors.updateInstructorCounts();
                
                VedMGClassRoomAdmin.showMessage('Instructor data refreshed successfully!', 'success');
            }).catch(function(error) {
                console.error('Failed to refresh instructors:', error);
                
                $button.prop('disabled', false);
                $button.text('Refresh Data');
                
                VedMGClassRoomAdmin.showMessage('Failed to refresh data. Please try again.', 'error');
            });
        },
        
        /**
         * Handle view courses
         */
        handleViewCourses: function(e) {
            e.preventDefault();
            
            var href = $(this).attr('href');
            
            if (href && href !== '#') {
                window.location.href = href;
            } else {
                // Fallback for placeholder links
                var instructorId = $(this).closest('tr').data('instructor-id');
                var instructorName = $(this).closest('tr').find('td:nth-child(2) strong').text();
                
                VedMGClassRoomAdmin.showMessage('Viewing courses for "' + instructorName + '" (Placeholder action)', 'info');
            }
        },
        
        /**
         * Extract instructor data from table row
         */
        extractInstructorData: function($row) {
            return {
                id: $row.data('instructor-id'),
                name: $row.find('td:nth-child(2) strong').text().trim(),
                email: $row.find('td:nth-child(3)').text().trim(),
                phone: $row.find('td:nth-child(4)').text().trim(),
                specialization: $row.find('td:nth-child(5)').text().trim(),
                courseCount: $row.find('td:nth-child(6) .vedmg-course-count').text().trim(),
                studentCount: $row.find('td:nth-child(7)').text().trim(),
                status: $row.find('.vedmg-instructor-status').data('status'),
                lastActivity: $row.find('td:nth-child(9)').text().trim()
            };
        },
        
        /**
         * Open instructor details modal
         */
        openInstructorDetailsModal: function(instructorData) {
            var $modal = $('#vedmg-instructor-details-modal');
            
            // Populate modal with instructor data
            $('#details-instructor-name').text(instructorData.name);
            $('#details-instructor-title').text(instructorData.specialization + ' Instructor');
            $('#details-instructor-status').text(instructorData.status).attr('data-status', instructorData.status);
            $('#details-instructor-email').text(instructorData.email);
            $('#details-instructor-phone').text(instructorData.phone || 'Not provided');
            $('#details-instructor-specialization').text(instructorData.specialization);
            $('#details-instructor-courses').text(instructorData.courseCount);
            $('#details-instructor-students').text(instructorData.studentCount);
            
            // Set avatar placeholder with first letter
            var firstLetter = instructorData.name.charAt(0).toUpperCase();
            $('.vedmg-avatar-placeholder').text(firstLetter);
            
            // Load course details, today's classes, upcoming classes, and assigned meetings
            this.loadInstructorCourses(instructorData.id);
            this.loadInstructorTodayClasses(instructorData.id);
            this.loadInstructorUpcomingClasses(instructorData.id);
            this.loadInstructorAssignedMeetings(instructorData.id);
            
            // Store instructor ID for reassignment modal
            $modal.data('instructor-id', instructorData.id);
            
            // Show modal with proper CSS
            $modal.addClass('vedmg-modal-show');
            $modal.show();
        },
        
        /**
         * Load instructor's courses
         */
        loadInstructorCourses: function(instructorId) {
            // For now, show placeholder data
            $('#details-instructor-course-list').html('<div class="vedmg-course-item">Loading courses...</div>');
            
            // TODO: Implement AJAX call to load real course data
            setTimeout(function() {
                $('#details-instructor-course-list').html(
                    // '<div class="vedmg-course-item">Mathematics - Advanced Level</div>' +
                    // '<div class="vedmg-course-item">Physics - Fundamentals</div>' +
                    '<div class="vedmg-no-courses" style="display:none;">No courses assigned</div>'
                );
            }, 500);
        },
        
        /**
         * Load instructor's today's classes
         */
        loadInstructorTodayClasses: function(instructorId) {
            var $container = $('#details-instructor-today-classes');
            $container.html('<p>Loading today\'s schedule...</p>');
            
            // Make AJAX call to get today's classes
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_instructor_today_classes',
                    instructor_id: instructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.sessions) {
                        var sessions = response.data.sessions;
                        var html = '';
                        
                        if (sessions.length > 0) {
                            sessions.forEach(function(session) {
                                html += '<div class="vedmg-today-class-item">';
                                html += '<div class="vedmg-class-time">' + session.start_time + ' - ' + session.end_time + '</div>';
                                html += '<div class="vedmg-class-title">' + session.session_title + '</div>';
                                html += '<div class="vedmg-class-course">Course: ' + session.course_name + '</div>';
                                html += '<div class="vedmg-class-students">' + session.enrolled_students + ' students enrolled</div>';
                                html += '</div>';
                            });
                        } else {
                            html = '<div class="vedmg-no-classes">No classes scheduled for today</div>';
                        }
                        
                        $container.html(html);
                    } else {
                        $container.html('<div class="vedmg-no-classes">Unable to load today\'s schedule</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="vedmg-no-classes">Error loading today\'s schedule</div>');
                }
            });
        },
        
        /**
         * Load instructor's upcoming classes
         */
        loadInstructorUpcomingClasses: function(instructorId) {
            var $container = $('#details-instructor-upcoming-classes');
            $container.html('<p>Loading upcoming schedule...</p>');
            
            // Make AJAX call to get upcoming classes
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_instructor_all_future_classes',
                    instructor_id: instructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        var todaySessions = response.data.today_sessions || [];
                        var futureSessions = response.data.future_sessions || [];
                        var html = '';

                        // Show today's remaining sessions first
                        if (todaySessions.length > 0) {
                            html += '<div class="vedmg-sessions-section">';
                            html += '<h4 class="vedmg-sessions-section-title">📅 Today\'s Remaining Classes</h4>';
                            todaySessions.forEach(function(session) {
                                var statusClass = session.session_timing === 'today_current' ? 'current' : 'upcoming';
                                html += '<div class="vedmg-upcoming-class-item ' + statusClass + '">';
                                html += '<div class="vedmg-upcoming-class-info">';
                                html += '<div class="vedmg-upcoming-class-date">' + session.scheduled_date + '</div>';
                                html += '<div class="vedmg-upcoming-class-time">' + session.start_time + ' - ' + session.end_time + '</div>';
                                html += '<div class="vedmg-upcoming-class-title">' + session.session_title + '</div>';
                                html += '<div class="vedmg-upcoming-class-course">' + session.course_name + '</div>';
                                html += '<div class="vedmg-upcoming-class-students">' + session.enrolled_students + ' students</div>';
                                if (session.google_meet_link) {
                                    html += '<div class="vedmg-upcoming-class-link"><a href="' + session.google_meet_link + '" target="_blank">🔗 Join Meeting</a></div>';
                                }
                                html += '</div>';
                                html += '</div>';
                            });
                            html += '</div>';
                        }

                        // Show future sessions
                        if (futureSessions.length > 0) {
                            html += '<div class="vedmg-sessions-section">';
                            html += '<h4 class="vedmg-sessions-section-title">🗓️ Upcoming Classes (' + futureSessions.length + ' sessions)</h4>';
                            futureSessions.forEach(function(session) {
                                html += '<div class="vedmg-upcoming-class-item future">';
                                html += '<div class="vedmg-upcoming-class-info">';
                                html += '<div class="vedmg-upcoming-class-date">' + session.scheduled_date + '</div>';
                                html += '<div class="vedmg-upcoming-class-time">' + session.start_time + ' - ' + session.end_time + '</div>';
                                html += '<div class="vedmg-upcoming-class-title">' + session.session_title + '</div>';
                                html += '<div class="vedmg-upcoming-class-course">' + session.course_name + '</div>';
                                html += '<div class="vedmg-upcoming-class-students">' + session.enrolled_students + ' students</div>';
                                if (session.is_recurring) {
                                    html += '<div class="vedmg-recurring-indicator">🔄 Recurring Session</div>';
                                }
                                html += '</div>';
                                html += '</div>';
                            });
                            html += '</div>';
                        }

                        // Show message if no sessions
                        if (todaySessions.length === 0 && futureSessions.length === 0) {
                            html = '<div class="vedmg-no-classes">No upcoming classes scheduled</div>';
                        }

                        $container.html(html);
                    } else {
                        $container.html('<div class="vedmg-no-classes">Unable to load upcoming schedule</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="vedmg-no-classes">Error loading upcoming schedule</div>');
                }
            });
        },
        
        /**
         * Handle show reassign modal
         */
        handleShowReassignModal: function(e) {
            e.preventDefault();
            
            var $detailsModal = $('#vedmg-instructor-details-modal');
            var instructorId = $detailsModal.data('instructor-id');
            var instructorName = $('#details-instructor-name').text();
            var instructorEmail = $('#details-instructor-email').text();
            
            // Hide details modal properly
            $detailsModal.removeClass('vedmg-modal-show');
            $detailsModal.hide();
            
            // Open reassignment modal
            VedMGInstructors.openReassignmentModal(instructorId, instructorName, instructorEmail);
        },
        
        /**
         * Open class reassignment modal
         */
        openReassignmentModal: function(instructorId, instructorName, instructorEmail) {
            var $modal = $('#vedmg-class-reassignment-modal');
            
            // Set from instructor data
            $('#reassign-from-instructor-id').val(instructorId);
            $('#reassign-from-name').text(instructorName);
            $('#reassign-from-email').text(instructorEmail);
            $('#reassign-from-avatar').text(instructorName.charAt(0).toUpperCase());
            
            // Load available instructors for reassignment
            this.loadAvailableInstructors(instructorId);
            
            // Load sessions to be reassigned
            this.loadSessionsForReassignment(instructorId);
            
            // Load assigned meetings to be reassigned
            this.loadMeetingsForReassignment(instructorId);
            
            // Show modal with proper CSS
            $modal.addClass('vedmg-modal-show');
            $modal.show();
        },
        
        /**
         * Load available instructors for reassignment
         */
        loadAvailableInstructors: function(excludeInstructorId) {
            var $select = $('#reassign-to-instructor');
            $select.html('<option value="">Loading instructors...</option>');
            
            // Make AJAX call to get available instructors
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_available_instructors',
                    exclude_instructor: excludeInstructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.instructors) {
                        var html = '<option value="">Select Target Instructor</option>';
                        response.data.instructors.forEach(function(instructor) {
                            html += '<option value="' + instructor.instructor_id + '">' + instructor.instructor_name + ' (' + instructor.instructor_email + ')</option>';
                        });
                        $select.html(html);
                    } else {
                        $select.html('<option value="">No other instructors available</option>');
                    }
                },
                error: function() {
                    $select.html('<option value="">Error loading instructors</option>');
                }
            });
        },
        
        /**
         * Load sessions for reassignment
         */
        loadSessionsForReassignment: function(instructorId) {
            var $container = $('#reassignment-sessions-list');
            $container.html('<p>Loading sessions...</p>');
            
            // Make AJAX call to get future sessions
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_instructor_future_sessions',
                    instructor_id: instructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.sessions) {
                        var sessions = response.data.sessions;
                        var html = '';
                        
                        if (sessions.length > 0) {
                            sessions.forEach(function(session) {
                                html += '<div class="vedmg-session-reassign-item" data-session-id="' + session.session_id + '">';
                                html += '<input type="checkbox" class="vedmg-session-checkbox" value="' + session.session_id + '" disabled>';
                                html += '<div class="vedmg-session-reassign-info">';
                                html += '<div class="vedmg-session-reassign-title">' + session.session_title + '</div>';
                                html += '<div class="vedmg-session-reassign-details">' + session.course_name + '</div>';
                                html += '</div>';
                                html += '<div class="vedmg-session-reassign-date">' + session.scheduled_date + '<br>' + session.start_time + '</div>';
                                html += '</div>';
                            });
                        } else {
                            html = '<div class="vedmg-no-classes">No future sessions to reassign</div>';
                        }
                        
                        $container.html(html);
                        
                        // Initialize the UI state
                        VedMGInstructors.updateSessionSelectionUI();
                    } else {
                        $container.html('<div class="vedmg-no-classes">Unable to load sessions</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="vedmg-no-classes">Error loading sessions</div>');
                }
            });
        },
        
        /**
         * Handle reassign classes
         */
        handleReassignClasses: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $button = $('#confirm-class-reassignment');
            var $spinner = $button.find('.vedmg-classroom-spinner');
            
            var fromInstructorId = $('#reassign-from-instructor-id').val();
            var toInstructorId = $('#reassign-to-instructor').val();
            var reassignmentType = $('input[name="reassignment_type"]:checked').val();
            
            if (!toInstructorId) {
                VedMGClassRoomAdmin.showMessage('Please select a target instructor', 'error');
                return;
            }
            
            // Prepare session IDs based on reassignment type
            var sessionIds = [];
            var meetingIds = [];
            
            if (reassignmentType === 'selected') {
                sessionIds = $('.vedmg-session-checkbox:checked').map(function() {
                    return $(this).val();
                }).get();
                
                meetingIds = $('.vedmg-meeting-checkbox:checked').map(function() {
                    return $(this).val();
                }).get();
                
                if (sessionIds.length === 0 && meetingIds.length === 0) {
                    VedMGClassRoomAdmin.showMessage('Please select at least one session or meeting to reassign', 'error');
                    return;
                }
            }
            
            // Show loading state
            $spinner.show();
            $button.prop('disabled', true);
            $button.text('Reassigning...');
            
            // Prepare AJAX data
            var ajaxData = {
                action: 'vedmg_classroom_action',
                action_type: 'reassign_instructor_classes',
                from_instructor_id: fromInstructorId,
                to_instructor_id: toInstructorId,
                nonce: vedmg_classroom_ajax.nonce
            };
            
            // Add session IDs if specific sessions are selected
            if (sessionIds.length > 0) {
                ajaxData.session_ids = sessionIds;
            }
            
            // Add meeting IDs if specific meetings are selected
            if (meetingIds.length > 0) {
                ajaxData.meeting_ids = meetingIds;
            }
            
            // Make AJAX call to reassign classes
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: ajaxData,
                success: function(response) {
                    if (response.success) {
                        var message = response.data.message || 'Classes reassigned successfully';
                        var details = [];
                        if (response.data.affected_sessions) {
                            details.push(response.data.affected_sessions + ' sessions moved');
                        }
                        if (response.data.affected_meetings) {
                            details.push(response.data.affected_meetings + ' meetings moved');
                        }
                        if (details.length > 0) {
                            message += ' (' + details.join(', ') + ')';
                        }
                        VedMGClassRoomAdmin.showMessage(message, 'success');
                        VedMGInstructors.closeModals();
                        
                        // Refresh the page to show updated data
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        VedMGClassRoomAdmin.showMessage(response.data.message || 'Failed to reassign classes', 'error');
                    }
                },
                error: function() {
                    VedMGClassRoomAdmin.showMessage('Error reassigning classes', 'error');
                },
                complete: function() {
                    // Hide loading state
                    $spinner.hide();
                    $button.prop('disabled', false);
                    $button.html('<span class="vedmg-classroom-spinner"></span>Reassign Classes');
                }
            });
        },
        
        /**
         * Open instructor edit modal
         */
        openInstructorEditModal: function(instructorData) {
            var $modal = $('#vedmg-instructor-edit-modal');
            
            // Populate form with instructor data
            $('#edit-instructor-id').val(instructorData.id);
            $('#edit-instructor-name').val(instructorData.name);
            $('#edit-instructor-email').val(instructorData.email);
            $('#edit-instructor-phone').val(instructorData.phone);
            $('#edit-instructor-specialization').val(instructorData.specialization);
            $('#edit-instructor-status').val(instructorData.status);
            
            // Show modal with proper CSS
            $modal.addClass('vedmg-modal-show');
            $modal.show();
        },
        
        /**
         * Close modals
         */
        closeModals: function() {
            $('.vedmg-modal').removeClass('vedmg-modal-show');
            setTimeout(function() {
                $('.vedmg-modal').hide();
            }, 300);
        },
        
        /**
         * Perform search
         */
        performSearch: function() {
            var searchTerm = $('#instructor-search').val().toLowerCase();
            
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var name = $row.find('td:nth-child(2)').text().toLowerCase();
                var email = $row.find('td:nth-child(3)').text().toLowerCase();
                var specialization = $row.find('td:nth-child(5)').text().toLowerCase();
                
                var matches = name.includes(searchTerm) || 
                             email.includes(searchTerm) || 
                             specialization.includes(searchTerm);
                
                if (matches || searchTerm === '') {
                    $row.show();
                } else {
                    $row.hide();
                }
            });
            
            // Update counts after search
            VedMGInstructors.updateInstructorCounts();
            
            // Only update pagination if explicitly called from search/filter actions
            // Don't auto-update to prevent resetting page state
        },
        
        /**
         * Handle save instructor
         */
        handleSaveInstructor: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $saveBtn = $form.find('#save-instructor-edit');
            var $spinner = $saveBtn.find('.vedmg-classroom-spinner');
            
            // Show loading state
            $saveBtn.addClass('loading').prop('disabled', true);
            if ($spinner.length) {
                $spinner.show();
            }
            
            // Collect form data
            var formData = {
                instructor_id: $('#edit-instructor-id').val(),
                instructor_name: $('#edit-instructor-name').val(),
                instructor_email: $('#edit-instructor-email').val(),
                instructor_phone: $('#edit-instructor-phone').val(),
                instructor_bio: $('#edit-instructor-bio').val(),
                status: $('#edit-instructor-status').val()
            };
            
            // Make AJAX call to save instructor data
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'save_instructor_data',
                    form_data: formData,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    $saveBtn.removeClass('loading').prop('disabled', false);
                    if ($spinner.length) {
                        $spinner.hide();
                    }
                    
                    if (response.success) {
                        VedMGClassRoomAdmin.showMessage('Instructor updated successfully!', 'success');
                        VedMGInstructors.closeModals();
                        
                        // Refresh the page data
                        VedMGInstructors.refreshInstructorData();
                    } else {
                        VedMGClassRoomAdmin.showMessage('Failed to update instructor: ' + response.data, 'error');
                    }
                },
                error: function() {
                    $saveBtn.removeClass('loading').prop('disabled', false);
                    if ($spinner.length) {
                        $spinner.hide();
                    }
                    VedMGClassRoomAdmin.showMessage('Failed to update instructor. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Handle pagination
         */
        handlePagination: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var action = $btn.attr('id');
            var page = $btn.data('page');
            
            if ($btn.hasClass('vedmg-pagination-btn') && !$btn.prop('disabled')) {
                if (action === 'pagination-first') {
                    VedMGInstructors.goToPage(1);
                } else if (action === 'pagination-prev') {
                    VedMGInstructors.goToPreviousPage();
                } else if (action === 'pagination-next') {
                    VedMGInstructors.goToNextPage();
                } else if (action === 'pagination-last') {
                    VedMGInstructors.goToLastPage();
                } else if (page) {
                    VedMGInstructors.goToPage(page);
                }
            }
        },
        
        /**
         * Handle page size change
         */
        handlePageSizeChange: function() {
            var pageSize = $(this).val();
            VedMGInstructors.setPageSize(pageSize);
        },
        
        /**
         * Go to specific page
         */
        goToPage: function(page) {
            var pageSize = parseInt($('#pagination-size-select').val()) || 10;
            var totalItems = parseInt($('#pagination-total').text()) || 0;
            var totalPages = Math.ceil(totalItems / pageSize);
            
            // Validate page number
            if (page < 1) page = 1;
            if (page > totalPages) page = totalPages;
            if (totalPages === 0) page = 1;
            
            console.log('Going to page:', page, 'of', totalPages, 'total pages');
            
            // Update pagination state
            $('.vedmg-pagination-btn').removeClass('active');
            $('[data-page="' + page + '"]').addClass('active');
            
            // Update pagination info
            var start = ((page - 1) * pageSize) + 1;
            var end = Math.min(page * pageSize, totalItems);
            
            if (totalItems === 0) {
                start = 0;
                end = 0;
            }
            
            $('#pagination-start').text(start);
            $('#pagination-end').text(end);
            
            // Update pagination buttons state
            $('#pagination-first, #pagination-prev').prop('disabled', page === 1 || totalPages <= 1);
            $('#pagination-next, #pagination-last').prop('disabled', page === totalPages || totalPages <= 1);
            
            // Show/hide rows based on current page
            VedMGInstructors.showRowsForPage(page, pageSize);
            
            console.log('Showing items', start, 'to', end, 'of', totalItems);
        },
        
        /**
         * Show rows for specific page
         */
        showRowsForPage: function(page, pageSize) {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var startIndex = (page - 1) * pageSize;
            var endIndex = startIndex + pageSize;
            
            // First hide all visible rows
            $visibleRows.hide();
            
            // Then show only the rows for current page
            $visibleRows.slice(startIndex, endIndex).show();
            
            console.log('Showing rows', startIndex, 'to', endIndex - 1, 'of', $visibleRows.length, 'visible rows');
        },
        
        /**
         * Update pagination display
         */
        updatePagination: function() {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var totalItems = $visibleRows.length;
            var pageSize = parseInt($('#pagination-size-select').val()) || 10;
            var totalPages = Math.ceil(totalItems / pageSize);
            
            // Get current page from active pagination button, default to 1
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page')) || 1;
            
            // Validate current page
            if (currentPage > totalPages) currentPage = 1;
            if (currentPage < 1) currentPage = 1;
            if (totalPages === 0) currentPage = 1;
            
            console.log('Updating pagination:', {
                totalItems: totalItems,
                pageSize: pageSize,
                totalPages: totalPages,
                currentPage: currentPage
            });
            
            // Update total count
            $('#pagination-total').text(totalItems);
            
            // Update pagination numbers
            var $paginationNumbers = $('.vedmg-pagination-numbers');
            $paginationNumbers.empty();
            
            // Only show pagination if we have items
            if (totalItems > 0 && totalPages > 1) {
                // Calculate which page numbers to show (max 5)
                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(totalPages, startPage + 4);
                
                // Adjust start if we're near the end
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }
                
                for (var i = startPage; i <= endPage; i++) {
                    var activeClass = i === currentPage ? ' active' : '';
                    $paginationNumbers.append('<button class="vedmg-pagination-btn' + activeClass + '" data-page="' + i + '">' + i + '</button>');
                }
                
                // Enable/disable navigation buttons
                $('#pagination-first, #pagination-prev').prop('disabled', currentPage === 1);
                $('#pagination-next, #pagination-last').prop('disabled', currentPage >= totalPages);
            } else {
                // Single page or no pages - disable all navigation
                $('#pagination-first, #pagination-prev, #pagination-next, #pagination-last').prop('disabled', true);
                
                // Show page 1 button if we have items
                if (totalItems > 0) {
                    $paginationNumbers.append('<button class="vedmg-pagination-btn active" data-page="1">1</button>');
                }
            }
            
            // Show the current page (don't reset to page 1 unless necessary)
            VedMGInstructors.goToPage(currentPage);
        },
        
        /**
         * Go to next page
         */
        goToNextPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage + 1);
        },
        
        /**
         * Go to previous page
         */
        goToPreviousPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage - 1);
        },
        
        /**
         * Go to last page
         */
        goToLastPage: function() {
            var pageSize = parseInt($('#pagination-size-select').val());
            var totalItems = parseInt($('#pagination-total').text());
            var lastPage = Math.ceil(totalItems / pageSize);
            this.goToPage(lastPage);
        },
        
        /**
         * Set page size
         */
        setPageSize: function(pageSize) {
            // Update pagination with new page size but stay on current page if possible
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page')) || 1;
            
            // Recalculate pagination
            VedMGInstructors.updatePagination();
            
            console.log('Page size changed to:', pageSize, 'staying on page:', currentPage);
        },
        
        /**
         * Refresh instructor data
         */
        refreshInstructorData: function() {
            // Simulate data refresh
            console.log('Refreshing instructor data...');
            VedMGClassRoomAdmin.showMessage('Instructor data refreshed!', 'info');
        },
        
        /**
         * Perform bulk action on selected instructors
         */
        performBulkAction: function(action, instructorIds) {
            // Show loading state
            $('#apply-instructor-bulk-action').prop('disabled', true);
            
            // Simulate bulk action
            setTimeout(function() {
                var message = '';
                
                switch(action) {
                    case 'activate':
                        message = 'Activated ' + instructorIds.length + ' instructors successfully!';
                        break;
                    case 'deactivate':
                        message = 'Deactivated ' + instructorIds.length + ' instructors successfully!';
                        break;
                    case 'send_email':
                        message = 'Email sent to ' + instructorIds.length + ' instructors successfully!';
                        break;
                    case 'export':
                        message = 'Exported data for ' + instructorIds.length + ' instructors successfully!';
                        break;
                    default:
                        message = 'Bulk action completed on ' + instructorIds.length + ' instructors.';
                }
                
                VedMGClassRoomAdmin.showMessage(message, 'success');
                
                // Clear selections
                $('.instructor-checkbox').prop('checked', false);
                $('#select-all-instructors').prop('checked', false);
                
                // Re-enable bulk action button
                $('#apply-instructor-bulk-action').prop('disabled', false);
                
                // Update counts
                VedMGInstructors.updateInstructorCounts();
                
            }, 2000);
        },
        
        /**
         * Update bulk action button state
         */
        updateBulkActionState: function() {
            var selectedCount = $('.instructor-checkbox:checked').length;
            var $bulkBtn = $('#apply-instructor-bulk-action');
            
            if (selectedCount > 0) {
                $bulkBtn.prop('disabled', false);
                $bulkBtn.text('Apply (' + selectedCount + ')');
            } else {
                $bulkBtn.prop('disabled', true);
                $bulkBtn.text('Apply');
            }
        },
        
        /**
         * Sync with MasterStudy LMS
         */
        syncWithMasterStudy: function() {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'sync_masterstudy_instructors',
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Refresh instructors data
         */
        refreshInstructorsData: function() {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: vedmg_classroom_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'vedmg_classroom_action',
                        action_type: 'refresh_instructors',
                        nonce: vedmg_classroom_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        /**
         * Update instructor counts
         */
        updateInstructorCounts: function() {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var activeCount = 0;
            var inactiveCount = 0;
            
            $visibleRows.each(function() {
                var status = $(this).find('.vedmg-instructor-status').attr('data-status');
                if (status === 'active') {
                    activeCount++;
                } else if (status === 'inactive') {
                    inactiveCount++;
                }
            });
            
            $('#active-instructor-count').text(activeCount);
            $('#inactive-instructor-count').text(inactiveCount);
            
            // Update statistics cards
            $('.vedmg-instructor-stats-grid .vedmg-stat-card').eq(0).find('.vedmg-stat-number').text($visibleRows.length);
            $('.vedmg-instructor-stats-grid .vedmg-stat-card').eq(1).find('.vedmg-stat-number').text(activeCount);
        },
        
        /**
         * Handle reassignment type change (All vs Selected)
         */
        handleReassignTypeChange: function() {
            VedMGInstructors.updateSessionSelectionUI();
        },
        
        /**
         * Update session selection UI based on reassignment type
         */
        updateSessionSelectionUI: function() {
            var reassignmentType = $('input[name="reassignment_type"]:checked').val();
            var $checkboxes = $('.vedmg-session-checkbox');
            var $bulkControls = $('.vedmg-bulk-session-controls');
            var $sessionItems = $('.vedmg-session-reassign-item');
            
            if (reassignmentType === 'selected') {
                // Enable individual selection
                $checkboxes.prop('disabled', false);
                $bulkControls.show();
                $sessionItems.removeClass('disabled');
            } else {
                // Disable individual selection (all sessions will be reassigned)
                $checkboxes.prop('disabled', true).prop('checked', false);
                $bulkControls.hide();
                $sessionItems.addClass('disabled');
            }
        },
        
        /**
         * Handle select all sessions
         */
        handleSelectAllSessions: function() {
            $('.vedmg-session-checkbox:not(:disabled)').prop('checked', true);
        },
        
        /**
         * Handle deselect all sessions
         */
        handleDeselectAllSessions: function() {
            $('.vedmg-session-checkbox:not(:disabled)').prop('checked', false);
        },
        
        /**
         * Handle individual session selection
         */
        handleSessionSelection: function() {
            // Could add individual session selection logic here if needed
            console.log('Session selection changed');
        },
        
        /**
         * Handle select all meetings
         */
        handleSelectAllMeetings: function() {
            $('.vedmg-meeting-checkbox:not(:disabled)').prop('checked', true);
        },
        
        /**
         * Handle deselect all meetings
         */
        handleDeselectAllMeetings: function() {
            $('.vedmg-meeting-checkbox:not(:disabled)').prop('checked', false);
        },
        
        /**
         * Handle individual meeting selection
         */
        handleMeetingSelection: function() {
            // Could add individual meeting selection logic here if needed
            console.log('Meeting selection changed');
        },
        
        /**
         * Load instructor's assigned meetings
         */
        loadInstructorAssignedMeetings: function(instructorId) {
            var $container = $('#details-instructor-assigned-meetings');
            $container.html('<p>Loading assigned meetings...</p>');
            
            // Make AJAX call to get assigned meetings
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_instructor_meetings',
                    instructor_id: instructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.meetings) {
                        var meetings = response.data.meetings;
                        var html = '';
                        
                        if (meetings.length > 0) {
                            meetings.forEach(function(meeting) {
                                var meetingDate = new Date(meeting.scheduled_date + ' ' + meeting.start_time);
                                var dateStr = meetingDate.toLocaleDateString();
                                var timeStr = meetingDate.toLocaleTimeString();
                                var isUpcoming = meetingDate > new Date();
                                
                                html += '<div class="vedmg-meeting-item ' + (isUpcoming ? 'upcoming' : 'past') + '">';
                                html += '  <div class="vedmg-meeting-header">';
                                html += '    <h5>' + meeting.session_title + '</h5>';
                                html += '    <span class="vedmg-meeting-status ' + meeting.session_status + '">' + meeting.session_status + '</span>';
                                html += '  </div>';
                                html += '  <div class="vedmg-meeting-details">';
                                html += '    <div class="vedmg-meeting-info">';
                                html += '      <span class="vedmg-meeting-date"><strong>Date:</strong> ' + dateStr + '</span>';
                                html += '      <span class="vedmg-meeting-time"><strong>Time:</strong> ' + timeStr + '</span>';
                                html += '      <span class="vedmg-meeting-course"><strong>Course:</strong> ' + (meeting.course_name || 'N/A') + '</span>';
                                html += '    </div>';
                                if (meeting.session_description) {
                                    html += '    <div class="vedmg-meeting-description">';
                                    html += '      <strong>Description:</strong> ' + meeting.session_description;
                                    html += '    </div>';
                                }
                                html += '  </div>';
                                html += '  <div class="vedmg-meeting-actions">';
                                if (meeting.google_meet_link && isUpcoming) {
                                    html += '    <a href="' + meeting.google_meet_link + '" target="_blank" class="vedmg-classroom-btn vedmg-classroom-btn-primary vedmg-join-meeting-btn">';
                                    html += '      🎥 Join Meeting';
                                    html += '    </a>';
                                } else if (meeting.google_meet_link && !isUpcoming) {
                                    html += '    <span class="vedmg-meeting-completed">Meeting Completed</span>';
                                } else {
                                    html += '    <span class="vedmg-meeting-no-link">No meeting link available</span>';
                                }
                                html += '  </div>';
                                html += '</div>';
                            });
                        } else {
                            html = '<div class="vedmg-no-meetings">No meetings assigned to this instructor.</div>';
                        }
                        
                        $container.html(html);
                    } else {
                        $container.html('<div class="vedmg-no-meetings">Failed to load meetings.</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="vedmg-no-meetings">Error loading meetings.</div>');
                }
            });
        },
        
        /**
         * Load meetings for reassignment
         */
        loadMeetingsForReassignment: function(instructorId) {
            var $container = $('#vedmg-reassignment-meetings .vedmg-reassignment-items');
            $container.html('<p>Loading assigned meetings...</p>');
            
            // Make AJAX call to get assigned meetings for reassignment
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'get_instructor_meetings',
                    instructor_id: instructorId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.meetings) {
                        var meetings = response.data.meetings;
                        var html = '';
                        
                        if (meetings.length > 0) {
                            // Only show upcoming meetings for reassignment
                            var upcomingMeetings = meetings.filter(function(meeting) {
                                var meetingDate = new Date(meeting.scheduled_date + ' ' + meeting.start_time);
                                return meetingDate > new Date() && meeting.google_meet_link && meeting.google_meet_link.trim() !== '';
                            });
                            
                            if (upcomingMeetings.length > 0) {
                                upcomingMeetings.forEach(function(meeting) {
                                    var meetingDate = new Date(meeting.scheduled_date + ' ' + meeting.start_time);
                                    var dateStr = meetingDate.toLocaleDateString();
                                    var timeStr = meetingDate.toLocaleTimeString();
                                    
                                    html += '<div class="vedmg-reassignment-item">';
                                    html += '  <label>';
                                    html += '    <input type="checkbox" class="vedmg-meeting-checkbox" value="' + meeting.session_id + '">';
                                    html += '    <div class="vedmg-reassignment-item-content">';
                                    html += '      <span class="vedmg-meeting-title">' + meeting.session_title + '</span>';
                                    html += '      <span class="vedmg-meeting-meta">';
                                    html += '        Date: ' + dateStr + ' at ' + timeStr;
                                    if (meeting.course_name) {
                                        html += ' | Course: ' + meeting.course_name;
                                    }
                                    html += '      </span>';
                                    html += '    </div>';
                                    html += '  </label>';
                                    html += '</div>';
                                });
                                
                                // Add bulk controls at the top
                                html = '<div class="vedmg-bulk-controls">' +
                                      '<button type="button" class="button" onclick="VedMGInstructors.handleSelectAllMeetings()">Select All</button> ' +
                                      '<button type="button" class="button" onclick="VedMGInstructors.handleDeselectAllMeetings()">Deselect All</button>' +
                                      '</div>' + html;
                            } else {
                                html = '<div class="vedmg-no-reassign-items">No upcoming meetings with meeting links to reassign.</div>';
                            }
                        } else {
                            html = '<div class="vedmg-no-reassign-items">No meetings assigned to this instructor.</div>';
                        }
                        
                        $container.html(html);
                    } else {
                        $container.html('<div class="vedmg-no-reassign-items">Failed to load meetings.</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="vedmg-no-reassign-items">Error loading meetings.</div>');
                }
            });
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        VedMGInstructors.init();
    });
    
    // Make instructors object available globally
    window.VedMGInstructors = VedMGInstructors;
    
})(jQuery);
